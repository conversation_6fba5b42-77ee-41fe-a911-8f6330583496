"""
增强版邮件数据生成器
支持生成上万条高质量、多样化的测试邮件

特点：
1. 更丰富的邮件模板（100+种）
2. 多语言支持（中英文混合）
3. 真实的邮件特征（域名、IP、时间戳等）
4. 分层数据生成（基础->变种->对抗样本）
5. 质量评估和去重

使用方法：
python enhanced_email_generator.py --count 10000 --output massive_test_emails.zip
"""

import os
import random
import zipfile
import argparse
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import json


class EnhancedEmailGenerator:
    """增强版邮件生成器"""
    
    def __init__(self):
        # 真实域名池（用于正常邮件）
        self.legitimate_domains = [
            'gmail.com', 'outlook.com', 'yahoo.com', 'hotmail.com',
            'qq.com', '163.com', '126.com', 'sina.com',
            'company.com', 'corp.net', 'business.org', 'enterprise.com',
            'tech.io', 'startup.co', 'innovation.ai', 'solutions.dev'
        ]
        
        # 可疑域名池（用于钓鱼邮件）
        self.suspicious_domains = [
            'secure-verify.com', 'account-check.net', 'user-confirm.org',
            'safety-update.info', 'security-alert.biz', 'verify-account.co',
            'bank-security.net', 'payment-verify.com', 'account-safety.org',
            'user-protection.info', 'secure-login.biz', 'verify-identity.co'
        ]
        
        # 新兴技术相关域名（用于零样本测试）
        self.emerging_domains = [
            'ai-verify.com', 'nft-security.net', 'metaverse-auth.org',
            'crypto-verify.info', 'blockchain-security.biz', 'web3-auth.co',
            'defi-verify.com', 'dao-security.net', 'virtual-auth.org'
        ]
        
        # 扩展的正常邮件模板库
        self.normal_templates = self._load_normal_templates()
        
        # 扩展的钓鱼邮件模板库
        self.phishing_templates = self._load_phishing_templates()
        
        # 零样本邮件模板库
        self.zero_shot_templates = self._load_zero_shot_templates()
        
        # 用于去重的哈希集合
        self.generated_hashes = set()
    
    def _load_normal_templates(self) -> List[str]:
        """加载正常邮件模板"""
        templates = [
            # 工作相关邮件
            """Subject: {subject_prefix}会议通知 - {meeting_topic}
From: {sender}@{domain}
To: {recipient}@{domain}
Date: {date}

{greeting}：

定于{meeting_date}在{location}召开{meeting_topic}会议。

会议议程：
- {agenda_1}
- {agenda_2}
- {agenda_3}

请准时参加。

{signature}
{department}""",

            # 系统通知邮件
            """Subject: System Maintenance - {system_name}
From: it-support@{domain}
To: all-users@{domain}
Date: {date}

Dear Users,

We will perform scheduled maintenance on {system_name}.

Maintenance Window:
- Start: {start_time}
- End: {end_time}
- Duration: {duration} hours

Affected Services:
- {service_1}
- {service_2}

Thank you for your patience.

IT Support Team""",

            # 项目更新邮件
            """Subject: Project Update - {project_name}
From: {sender}@{domain}
To: team@{domain}
Date: {date}

Team,

Here's the latest update on {project_name}:

Progress:
- Completed: {completed_tasks}
- In Progress: {ongoing_tasks}
- Upcoming: {upcoming_tasks}

Milestones:
- Next Deadline: {next_deadline}
- Overall Progress: {progress_percent}%

Best regards,
{sender_name}
Project Manager""",

            # 培训通知邮件
            """Subject: Training Session - {training_topic}
From: hr@{domain}
To: employees@{domain}
Date: {date}

Dear Colleagues,

We are organizing a training session on {training_topic}.

Details:
- Date: {training_date}
- Time: {training_time}
- Location: {training_location}
- Trainer: {trainer_name}

Registration: {registration_link}

HR Department""",

            # 客户服务邮件
            """Subject: Thank you for your order #{order_number}
From: orders@{domain}
To: {customer_email}
Date: {date}

Dear {customer_name},

Thank you for your recent order.

Order Details:
- Order #: {order_number}
- Items: {item_count}
- Total: ${total_amount}
- Status: {order_status}

Tracking: {tracking_number}

Customer Service Team""",
        ]
        
        # 添加更多中文模板
        chinese_templates = [
            """Subject: 工作汇报 - {report_period}
From: {sender}@{domain}
To: manager@{domain}
Date: {date}

{manager_name}经理：

现将{report_period}工作情况汇报如下：

完成工作：
- {task_1}
- {task_2}
- {task_3}

下周计划：
- {plan_1}
- {plan_2}

{sender_name}
{date_short}""",

            """Subject: 产品发布通知
From: product@{domain}
To: all@{domain}
Date: {date}

各位同事：

我们很高兴地宣布{product_name}正式发布。

产品特性：
- {feature_1}
- {feature_2}
- {feature_3}

发布时间：{release_date}

产品团队""",
        ]
        
        return templates + chinese_templates
    
    def _load_phishing_templates(self) -> List[str]:
        """加载钓鱼邮件模板"""
        return [
            # 银行钓鱼
            """Subject: 【{bank_name}】紧急安全警报
From: security@{fake_domain}
To: <EMAIL>
Date: {date}

尊敬的客户：

我们检测到您的账户存在{threat_type}。

风险详情：
- 账户：****{account_suffix}
- 风险等级：{risk_level}
- 检测时间：{detection_time}

立即验证：{phishing_url}

必须在{deadline}小时内完成，否则账户将被{consequence}。

{bank_name}安全中心
客服热线：{fake_phone}""",

            # 电商钓鱼
            """Subject: Your {platform} Account - Urgent Action Required
From: security@{fake_domain}
To: <EMAIL>
Date: {date}

Dear Valued Customer,

Your {platform} account has been {action_taken} due to {reason}.

Account Information:
- User ID: {user_id}
- Status: {account_status}
- Last Login: {last_login}

Immediate Action Required:
{phishing_url}

Failure to verify within {deadline} hours will result in {consequence}.

{platform} Security Team
Reference: {reference_number}""",

            # 支付钓鱼
            """Subject: Payment Verification Required - Transaction #{transaction_id}
From: payments@{fake_domain}
To: <EMAIL>
Date: {date}

Dear Customer,

We need to verify a recent transaction on your account.

Transaction Details:
- ID: {transaction_id}
- Amount: ${amount}
- Merchant: {merchant_name}
- Status: Pending Verification

Verify Transaction: {phishing_url}

This verification must be completed within {deadline} hours.

Payment Security Team""",
        ]
    
    def _load_zero_shot_templates(self) -> List[str]:
        """加载零样本测试邮件模板（新兴攻击类型）"""
        return [
            # AI服务钓鱼
            """Subject: AI Model Access Expiring - {ai_service}
From: ai-support@{fake_domain}
To: <EMAIL>
Date: {date}

Hello AI Researcher,

Your {ai_service} premium access expires in {expire_days} days.

Current Usage:
- Model Calls: {api_calls}
- Compute Hours: {compute_hours}
- Storage: {storage_used}GB

Renew Access: {phishing_url}

Features at Risk:
• Advanced model access
• Priority processing
• Extended context windows
• Custom fine-tuning

AI Platform Team""",

            # NFT/区块链钓鱼
            """Subject: NFT Collection Verification - New Regulations
From: compliance@{fake_domain}
To: <EMAIL>
Date: {date}

Dear NFT Collector,

New blockchain compliance regulations require immediate verification.

Your Collection:
- Wallet: {wallet_address}
- NFTs: {nft_count} items
- Estimated Value: ${collection_value}
- Blockchain: {blockchain_name}

Compliance Verification: {phishing_url}

Deadline: {deadline} hours
Non-compliance may result in asset freezing.

Blockchain Compliance Authority""",

            # 元宇宙钓鱼
            """Subject: Metaverse Identity Verification Required
From: metaverse-id@{fake_domain}
To: <EMAIL>
Date: {date}

Virtual Citizen,

The Metaverse Identity System requires verification update.

Your Virtual Profile:
- Avatar: {avatar_name}
- Virtual Assets: ${virtual_assets}
- Worlds Accessed: {world_count}
- Identity Score: {identity_score}

Update Identity: {phishing_url}

New security protocols mandate verification within {deadline} hours.

Metaverse Identity Authority""",
        ]

    def generate_realistic_metadata(self, email_type: str) -> Dict:
        """生成真实的邮件元数据"""
        now = datetime.now()
        days_ago = random.randint(0, 30)
        email_date = now - timedelta(days=days_ago)

        if email_type == "normal":
            domain = random.choice(self.legitimate_domains)
            sender_name = random.choice([
                "张伟", "李娜", "王强", "刘敏", "陈静",
                "John Smith", "Sarah Johnson", "Mike Brown", "Lisa Davis"
            ])
        elif email_type == "phishing":
            domain = random.choice(self.suspicious_domains)
            sender_name = random.choice([
                "Security Team", "Account Manager", "Customer Service",
                "安全中心", "客户经理", "技术支持"
            ])
        else:  # zero_shot
            domain = random.choice(self.emerging_domains)
            sender_name = random.choice([
                "AI Support", "Blockchain Team", "Metaverse Admin",
                "NFT Compliance", "Web3 Security", "DeFi Support"
            ])

        return {
            'domain': domain,
            'sender_name': sender_name,
            'date': email_date.strftime("%a, %d %b %Y %H:%M:%S +0800"),
            'date_short': email_date.strftime("%Y-%m-%d"),
            'message_id': f"<{random.randint(100000, 999999)}.{int(email_date.timestamp())}@{domain}>",
            'ip_address': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"
        }

    def generate_email_variants(self, base_content: str, num_variants: int = 3) -> List[str]:
        """生成邮件变种（语义保持，表达方式不同）"""
        variants = [base_content]

        # 简单的变种策略
        for i in range(num_variants - 1):
            variant = base_content

            # 替换同义词
            replacements = {
                'urgent': ['immediate', 'critical', 'pressing'],
                'verify': ['confirm', 'validate', 'check'],
                'click': ['tap', 'select', 'access'],
                'account': ['profile', 'credentials', 'login'],
                '紧急': ['急迫', '重要', '关键'],
                '验证': ['确认', '核实', '检查'],
                '点击': ['单击', '选择', '访问']
            }

            for original, alternatives in replacements.items():
                if original in variant:
                    variant = variant.replace(original, random.choice(alternatives))

            variants.append(variant)

        return variants

    def generate_massive_dataset(self, total_count: int,
                                normal_ratio: float = 0.6,
                                variants_per_template: int = 5) -> List[Tuple[str, str, int, str]]:
        """
        生成大规模数据集

        Args:
            total_count: 总邮件数量
            normal_ratio: 正常邮件比例
            variants_per_template: 每个模板生成的变种数量

        Returns:
            邮件列表 (filename, content, label, type)
        """
        print(f"🚀 开始生成 {total_count:,} 个邮件...")

        normal_count = int(total_count * normal_ratio)
        phishing_count = int(total_count * 0.25)  # 25% 传统钓鱼
        zero_shot_count = total_count - normal_count - phishing_count  # 剩余为零样本

        print(f"📊 邮件分布：")
        print(f"   正常邮件: {normal_count:,}")
        print(f"   钓鱼邮件: {phishing_count:,}")
        print(f"   零样本邮件: {zero_shot_count:,}")

        all_emails = []

        # 生成正常邮件
        print("📝 生成正常邮件...")
        for i in range(normal_count):
            template = random.choice(self.normal_templates)
            metadata = self.generate_realistic_metadata("normal")
            content = self._fill_template(template, metadata, "normal")

            # 生成变种
            variants = self.generate_email_variants(content, min(variants_per_template, 3))
            for j, variant in enumerate(variants[:min(len(variants), normal_count - len(all_emails))]):
                if len(all_emails) >= normal_count:
                    break

                filename = f"normal_{len(all_emails)+1:06d}.txt"
                all_emails.append((filename, variant, 0, "normal"))

        # 生成钓鱼邮件
        print("🎣 生成钓鱼邮件...")
        phishing_generated = 0
        for i in range(phishing_count):
            template = random.choice(self.phishing_templates)
            metadata = self.generate_realistic_metadata("phishing")
            content = self._fill_template(template, metadata, "phishing")

            variants = self.generate_email_variants(content, min(variants_per_template, 3))
            for j, variant in enumerate(variants[:min(len(variants), phishing_count - phishing_generated)]):
                if phishing_generated >= phishing_count:
                    break

                filename = f"phishing_{phishing_generated+1:06d}.txt"
                all_emails.append((filename, variant, 1, "phishing"))
                phishing_generated += 1

        # 生成零样本邮件
        print("🔬 生成零样本邮件...")
        zero_shot_generated = 0
        for i in range(zero_shot_count):
            template = random.choice(self.zero_shot_templates)
            metadata = self.generate_realistic_metadata("zero_shot")
            content = self._fill_template(template, metadata, "zero_shot")

            variants = self.generate_email_variants(content, min(variants_per_template, 2))
            for j, variant in enumerate(variants[:min(len(variants), zero_shot_count - zero_shot_generated)]):
                if zero_shot_generated >= zero_shot_count:
                    break

                filename = f"zero_shot_{zero_shot_generated+1:06d}.txt"
                all_emails.append((filename, variant, 1, "zero_shot"))
                zero_shot_generated += 1

        # 随机打乱
        random.shuffle(all_emails)

        print(f"✅ 生成完成！总计 {len(all_emails):,} 个邮件")
        return all_emails

    def _fill_template(self, template: str, metadata: Dict, email_type: str) -> str:
        """填充邮件模板"""
        # 基础变量
        variables = {
            'domain': metadata['domain'],
            'sender_name': metadata['sender_name'],
            'date': metadata['date'],
            'date_short': metadata['date_short'],
            'sender': f"user{random.randint(1, 999)}",
            'recipient': f"user{random.randint(1, 999)}",
        }

        # 根据邮件类型添加特定变量
        if email_type == "normal":
            variables.update(self._get_normal_variables())
        elif email_type == "phishing":
            variables.update(self._get_phishing_variables())
        else:  # zero_shot
            variables.update(self._get_zero_shot_variables())

        # 填充模板
        try:
            return template.format(**variables)
        except KeyError as e:
            # 如果有缺失的变量，用默认值填充
            missing_var = str(e).strip("'")
            variables[missing_var] = f"[{missing_var}]"
            return template.format(**variables)

    def _get_normal_variables(self) -> Dict:
        """获取正常邮件的变量"""
        return {
            'subject_prefix': random.choice(['', '[重要] ', '[通知] ', 'Re: ']),
            'greeting': random.choice(['各位同事', '大家好', 'Dear Team', 'Hello Everyone']),
            'meeting_topic': random.choice(['项目评审', '季度总结', '培训会议', '团建活动']),
            'meeting_date': f"{random.randint(1, 28)}日",
            'location': random.choice(['会议室A', '会议室B', '多功能厅', '培训室']),
            'agenda_1': '工作汇报',
            'agenda_2': '问题讨论',
            'agenda_3': '下步计划',
            'signature': random.choice(['此致', '谢谢', 'Best regards', 'Thank you']),
            'department': random.choice(['技术部', '市场部', '人事部', '财务部']),
            'system_name': random.choice(['Email System', 'File Server', 'Database', 'Web Portal']),
            'start_time': f"{random.randint(20, 23)}:00",
            'end_time': f"{random.randint(1, 6)}:00",
            'duration': random.randint(2, 8),
            'service_1': 'Email Service',
            'service_2': 'File Storage',
            'project_name': random.choice(['Alpha Project', 'Beta System', 'Gamma Platform']),
            'completed_tasks': f"{random.randint(5, 15)} tasks",
            'ongoing_tasks': f"{random.randint(2, 8)} tasks",
            'upcoming_tasks': f"{random.randint(3, 10)} tasks",
            'next_deadline': f"{random.randint(1, 30)}日",
            'progress_percent': random.randint(60, 95),
            'training_topic': random.choice(['安全培训', '技能提升', '新系统培训']),
            'training_date': f"{random.randint(1, 30)}日",
            'training_time': f"{random.randint(9, 17)}:00",
            'training_location': random.choice(['培训室', '会议厅', '在线会议']),
            'trainer_name': random.choice(['张老师', '李专家', 'Dr. Smith']),
            'registration_link': 'http://training.company.com/register',
            'order_number': f"ORD{random.randint(100000, 999999)}",
            'customer_email': '<EMAIL>',
            'customer_name': random.choice(['张先生', '李女士', 'Mr. Johnson']),
            'item_count': random.randint(1, 5),
            'total_amount': f"{random.randint(50, 500)}.{random.randint(10, 99)}",
            'order_status': 'Processing',
            'tracking_number': f"TRK{random.randint(*********, *********)}",
            'report_period': random.choice(['本周', '本月', '第一季度']),
            'manager_name': random.choice(['王', '李', '张']),
            'task_1': '完成项目文档',
            'task_2': '代码审查',
            'task_3': '参加团队会议',
            'plan_1': '开始新功能开发',
            'plan_2': '准备演示文稿',
            'product_name': random.choice(['智能助手', '数据平台', '管理系统']),
            'feature_1': '智能分析',
            'feature_2': '实时监控',
            'feature_3': '自动报告',
            'release_date': f"{random.randint(1, 30)}日"
        }

    def _get_phishing_variables(self) -> Dict:
        """获取钓鱼邮件的变量"""
        return {
            'bank_name': random.choice(['工商银行', '建设银行', '农业银行', '中国银行']),
            'fake_domain': random.choice(self.suspicious_domains),
            'threat_type': random.choice(['异常登录', '可疑交易', '安全风险']),
            'account_suffix': f"{random.randint(1000, 9999)}",
            'risk_level': random.choice(['高危', '严重', '紧急']),
            'detection_time': f"{random.randint(1, 12)}:{random.randint(10, 59)}",
            'phishing_url': f"http://{random.choice(self.suspicious_domains)}/verify",
            'deadline': random.randint(12, 48),
            'consequence': random.choice(['冻结', '关闭', '限制使用']),
            'fake_phone': f"400-{random.randint(100, 999)}-{random.randint(1000, 9999)}",
            'platform': random.choice(['Amazon', 'PayPal', 'eBay', 'Alibaba']),
            'action_taken': random.choice(['suspended', 'limited', 'locked']),
            'reason': random.choice(['suspicious activity', 'security breach', 'policy violation']),
            'user_id': f"USR{random.randint(100000, 999999)}",
            'account_status': 'Under Review',
            'last_login': f"{random.randint(1, 30)} days ago",
            'reference_number': f"REF{random.randint(100000, 999999)}",
            'transaction_id': f"TXN{random.randint(*********, *********)}",
            'amount': f"{random.randint(100, 2000)}.{random.randint(10, 99)}",
            'merchant_name': random.choice(['Online Store', 'Digital Services', 'Tech Shop'])
        }

    def _get_zero_shot_variables(self) -> Dict:
        """获取零样本邮件的变量"""
        return {
            'fake_domain': random.choice(self.emerging_domains),
            'ai_service': random.choice(['ChatGPT Plus', 'Claude Pro', 'Gemini Advanced', 'GPT-4 API']),
            'expire_days': random.randint(1, 7),
            'api_calls': f"{random.randint(10000, 100000):,}",
            'compute_hours': f"{random.randint(100, 1000)}",
            'storage_used': random.randint(50, 500),
            'phishing_url': f"http://{random.choice(self.emerging_domains)}/renew",
            'wallet_address': f"0x{random.randint(10**15, 10**16-1):016x}",
            'nft_count': random.randint(5, 100),
            'collection_value': f"{random.randint(10000, 500000):,}",
            'blockchain_name': random.choice(['Ethereum', 'Polygon', 'Solana', 'Binance Smart Chain']),
            'deadline': random.randint(24, 72),
            'avatar_name': f"Avatar{random.randint(1000, 9999)}",
            'virtual_assets': f"{random.randint(5000, 100000):,}",
            'world_count': random.randint(5, 50),
            'identity_score': random.randint(700, 999)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强版邮件数据生成器')
    parser.add_argument('--count', type=int, default=10000,
                       help='生成邮件数量 (默认: 10000)')
    parser.add_argument('--output', type=str, default='massive_test_emails.zip',
                       help='输出ZIP文件名')
    parser.add_argument('--normal-ratio', type=float, default=0.6,
                       help='正常邮件比例 (默认: 0.6)')
    parser.add_argument('--variants', type=int, default=3,
                       help='每个模板的变种数量 (默认: 3)')
    parser.add_argument('--quality-check', action='store_true',
                       help='启用质量检查和去重')

    args = parser.parse_args()

    print("🚀 增强版邮件数据生成器")
    print("=" * 50)
    print(f"目标数量: {args.count:,} 个邮件")
    print(f"正常邮件比例: {args.normal_ratio:.1%}")
    print(f"输出文件: {args.output}")
    print("=" * 50)

    # 创建生成器
    generator = EnhancedEmailGenerator()

    # 生成邮件
    emails = generator.generate_massive_dataset(
        total_count=args.count,
        normal_ratio=args.normal_ratio,
        variants_per_template=args.variants
    )

    # 质量检查
    if args.quality_check:
        print("🔍 执行质量检查...")
        emails = quality_filter(emails)
        print(f"✅ 质量检查完成，保留 {len(emails):,} 个高质量邮件")

    # 创建ZIP文件
    print(f"📦 创建ZIP文件: {args.output}")
    with zipfile.ZipFile(args.output, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
        for filename, content, label, email_type in emails:
            zipf.writestr(filename, content)

    # 生成标签文件
    label_file = args.output.replace('.zip', '_labels.csv')
    print(f"📝 创建标签文件: {label_file}")
    with open(label_file, 'w', encoding='utf-8') as f:
        f.write("filename,label,type,is_phishing\n")
        for filename, content, label, email_type in emails:
            is_phishing = "true" if label == 1 else "false"
            f.write(f"{filename},{label},{email_type},{is_phishing}\n")

    # 生成统计报告
    stats = generate_statistics(emails)
    stats_file = args.output.replace('.zip', '_stats.json')
    print(f"📊 创建统计报告: {stats_file}")
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)

    print("\n✅ 生成完成！")
    print(f"📁 文件列表：")
    print(f"   - {args.output} ({get_file_size(args.output)})")
    print(f"   - {label_file}")
    print(f"   - {stats_file}")

    print(f"\n📋 使用方法：")
    print(f"1. 启动后端: cd backend && python manage.py runserver")
    print(f"2. 启动前端: cd frontend && npm run dev")
    print(f"3. 访问: http://localhost:5173/batch-detection")
    print(f"4. 上传: {args.output}")


def quality_filter(emails: List[Tuple[str, str, int, str]]) -> List[Tuple[str, str, int, str]]:
    """质量过滤和去重"""
    seen_hashes = set()
    filtered_emails = []

    for filename, content, label, email_type in emails:
        # 计算内容哈希
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()

        # 去重
        if content_hash in seen_hashes:
            continue
        seen_hashes.add(content_hash)

        # 质量检查
        if len(content.strip()) < 50:  # 内容太短
            continue
        if content.count('\n') < 3:  # 结构太简单
            continue

        filtered_emails.append((filename, content, label, email_type))

    return filtered_emails


def generate_statistics(emails: List[Tuple[str, str, int, str]]) -> Dict:
    """生成统计信息"""
    total_count = len(emails)
    normal_count = sum(1 for _, _, label, _ in emails if label == 0)
    phishing_count = sum(1 for _, _, label, email_type in emails if label == 1 and email_type == "phishing")
    zero_shot_count = sum(1 for _, _, label, email_type in emails if label == 1 and email_type == "zero_shot")

    # 计算平均长度
    avg_length = sum(len(content) for _, content, _, _ in emails) / total_count

    return {
        "generation_time": datetime.now().isoformat(),
        "total_emails": total_count,
        "distribution": {
            "normal": normal_count,
            "phishing": phishing_count,
            "zero_shot": zero_shot_count
        },
        "percentages": {
            "normal": round(normal_count / total_count * 100, 2),
            "phishing": round(phishing_count / total_count * 100, 2),
            "zero_shot": round(zero_shot_count / total_count * 100, 2)
        },
        "average_length": round(avg_length, 2),
        "quality_metrics": {
            "min_length": min(len(content) for _, content, _, _ in emails),
            "max_length": max(len(content) for _, content, _, _ in emails),
            "unique_emails": len(set(content for _, content, _, _ in emails))
        }
    }


def get_file_size(filepath: str) -> str:
    """获取文件大小的可读格式"""
    size = os.path.getsize(filepath)
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024:
            return f"{size:.1f} {unit}"
        size /= 1024
    return f"{size:.1f} TB"


if __name__ == "__main__":
    main()
