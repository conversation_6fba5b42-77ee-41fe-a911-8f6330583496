"""
批量检测功能测试脚本
验证批量检测API的功能

使用方法：
python test_batch_detection.py --zip test_emails_100.zip --mode standard
"""

import requests
import time
import argparse
import json


class BatchDetectionTester:
    """批量检测测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/detection"
    
    def test_batch_upload(self, zip_file_path):
        """测试批量上传"""
        print(f"📤 测试批量上传: {zip_file_path}")
        
        try:
            with open(zip_file_path, 'rb') as f:
                files = {'zip_file': f}
                response = requests.post(f"{self.api_base}/batch/upload/", files=files)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 上传成功:")
                print(f"   批量ID: {data['batch_id']}")
                print(f"   文件数: {data['total_files']}")
                return data['batch_id']
            else:
                print(f"❌ 上传失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 上传异常: {e}")
            return None
    
    def test_batch_detect(self, batch_id, detection_mode="standard"):
        """测试开始批量检测"""
        print(f"🔍 开始批量检测: {batch_id}")
        
        try:
            data = {
                'batch_id': batch_id,
                'detection_mode': detection_mode
            }
            response = requests.post(f"{self.api_base}/batch/detect/", json=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 检测启动成功: {result['message']}")
                return True
            else:
                print(f"❌ 检测启动失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 检测启动异常: {e}")
            return False
    
    def monitor_batch_status(self, batch_id, max_wait_time=300):
        """监控批量检测状态"""
        print(f"📊 监控检测状态...")
        
        start_time = time.time()
        last_processed = 0
        
        while time.time() - start_time < max_wait_time:
            try:
                response = requests.get(f"{self.api_base}/batch/status/{batch_id}/")
                
                if response.status_code == 200:
                    status = response.json()
                    
                    # 显示进度
                    if status['processed_files'] != last_processed:
                        print(f"   进度: {status['processed_files']}/{status['total_files']} "
                              f"({status['progress']:.1f}%) "
                              f"速度: {status['processing_speed']:.1f} 文件/秒")
                        last_processed = status['processed_files']
                    
                    # 检查是否完成
                    if status['status'] == 'completed':
                        print(f"✅ 检测完成!")
                        return True
                    elif status['status'] == 'failed':
                        print(f"❌ 检测失败: {status.get('error', '未知错误')}")
                        return False
                    
                    time.sleep(2)  # 等待2秒后再次检查
                    
                else:
                    print(f"❌ 获取状态失败: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ 状态监控异常: {e}")
                return False
        
        print(f"⏰ 检测超时 ({max_wait_time}秒)")
        return False
    
    def get_batch_results(self, batch_id):
        """获取批量检测结果"""
        print(f"📋 获取检测结果...")
        
        try:
            response = requests.get(f"{self.api_base}/batch/results/{batch_id}/")
            
            if response.status_code == 200:
                results = response.json()
                
                print(f"✅ 结果获取成功:")
                summary = results['summary']
                print(f"   总邮件数: {summary['total_emails']}")
                print(f"   钓鱼邮件: {summary['phishing_emails']}")
                print(f"   正常邮件: {summary['normal_emails']}")
                print(f"   钓鱼率: {summary['phishing_rate']:.1f}%")
                print(f"   平均置信度: {summary['average_confidence']:.3f}")
                
                # 显示前几个结果
                print(f"\n📄 前5个检测结果:")
                for i, result in enumerate(results['results'][:5]):
                    status_text = "钓鱼" if result['is_phishing'] else "正常"
                    print(f"   {i+1}. {result['filename']}: {status_text} "
                          f"(置信度: {result['confidence']:.3f})")
                
                return results
            else:
                print(f"❌ 获取结果失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取结果异常: {e}")
            return None
    
    def test_export_results(self, batch_id):
        """测试结果导出"""
        print(f"📤 测试结果导出...")
        
        try:
            response = requests.get(f"{self.api_base}/batch/export/{batch_id}/")
            
            if response.status_code == 200:
                # 保存CSV文件
                filename = f"batch_results_{batch_id[:8]}.csv"
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 导出成功: {filename}")
                return filename
            else:
                print(f"❌ 导出失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 导出异常: {e}")
            return None
    
    def run_full_test(self, zip_file_path, detection_mode="standard"):
        """运行完整测试"""
        print(f"🚀 开始批量检测完整测试")
        print(f"   ZIP文件: {zip_file_path}")
        print(f"   检测模式: {detection_mode}")
        print("=" * 50)
        
        # 1. 上传文件
        batch_id = self.test_batch_upload(zip_file_path)
        if not batch_id:
            return False
        
        print()
        
        # 2. 开始检测
        if not self.test_batch_detect(batch_id, detection_mode):
            return False
        
        print()
        
        # 3. 监控状态
        if not self.monitor_batch_status(batch_id):
            return False
        
        print()
        
        # 4. 获取结果
        results = self.get_batch_results(batch_id)
        if not results:
            return False
        
        print()
        
        # 5. 导出结果
        export_file = self.test_export_results(batch_id)
        
        print()
        print("=" * 50)
        print("🎉 批量检测测试完成!")
        
        if export_file:
            print(f"📊 结果已导出到: {export_file}")
        
        return True


def main():
    parser = argparse.ArgumentParser(description='测试批量检测功能')
    parser.add_argument('--zip', type=str, required=True, help='ZIP文件路径')
    parser.add_argument('--mode', type=str, default='standard', 
                       choices=['standard', 'zero_shot'], help='检测模式')
    parser.add_argument('--url', type=str, default='http://localhost:8000', 
                       help='后端服务URL')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = BatchDetectionTester(args.url)
    
    # 运行测试
    success = tester.run_full_test(args.zip, args.mode)
    
    if success:
        print("\n✅ 所有测试通过!")
    else:
        print("\n❌ 测试失败!")
        exit(1)


if __name__ == "__main__":
    main()
