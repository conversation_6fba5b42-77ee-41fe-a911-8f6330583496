# 钓鱼邮件检测GCN+NLP混合模型

## 项目概述

本项目实现了一个基于图卷积网络(GCN)和自然语言处理(NLP)的钓鱼邮件检测系统，核心创新点是多模态特征融合的图卷积优化模型。

## 创新点

### 创新点一：多模态特征融合的图卷积优化模型

**创新依据：**
- 现有研究仅利用邮件正文文本构建GCN，忽略了附件、URL链接等关键信息
- 实际钓鱼邮件常包含多种模态的恶意特征

**技术方案：**
1. **扩展图结构**：构建包含文本、链接、元数据的异构图
   - 邮件元数据节点：发件人域名、IP地址
   - 附件特征节点：文件类型、哈希值
   - URL链接节点：链接结构特征

2. **图注意力机制**：使用GAT对异构节点进行加权聚合

3. **动态特征融合**：通过门控机制(GRU)自适应融合多模态特征

## 技术架构

```
输入邮件
    ├── 文本特征提取 (BERT/RoBERTa)
    ├── URL特征分析
    ├── 附件特征提取
    └── 元数据特征提取
         ↓
    异构图构建
         ↓
    图注意力网络 (GAT)
         ↓
    动态特征融合 (门控机制)
         ↓
    图卷积网络 (GCN)
         ↓
    分类预测 (钓鱼/正常)
```

## 环境要求

- Python 3.8+
- PyTorch 1.12+
- PyTorch Geometric
- Transformers
- NetworkX
- scikit-learn
- pandas
- numpy

## 项目结构

```
├── data/                   # 数据集
├── src/                    # 源代码
│   ├── models/            # 模型定义
│   ├── data_processing/   # 数据预处理
│   ├── feature_extraction/ # 特征提取
│   ├── graph_construction/ # 图构建
│   └── utils/             # 工具函数
├── experiments/           # 实验脚本
├── results/              # 实验结果
└── docs/                 # 文档
```

## 快速开始

1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 数据预处理
```bash
python src/data_processing/preprocess.py
```

3. 训练模型
```bash
python experiments/train.py
```

4. 评估模型
```bash
python experiments/evaluate.py
```

## 实验结果

TODO: 添加实验结果和对比分析

## 参考文献

TODO: 添加相关参考文献

## 作者

[你的姓名] - 硕士毕业设计项目
