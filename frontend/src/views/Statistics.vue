<template>
  <div class="statistics">
    <!-- 时间范围选择 -->
    <el-card class="time-selector">
      <div class="time-controls">
        <span>统计时间范围：</span>
        <el-radio-group v-model="selectedDays" @change="handleTimeChange">
          <el-radio-button :value="7">最近7天</el-radio-button>
          <el-radio-button :value="30">最近30天</el-radio-button>
          <el-radio-button :value="90">最近90天</el-radio-button>
        </el-radio-group>
        <el-button type="primary" @click="refreshData" style="margin-left: 20px;">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon size="40" color="#409EFF"><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats?.total_detections || 0 }}</div>
              <div class="stat-label">总检测数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon size="40" color="#F56C6C"><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats?.phishing_count || 0 }}</div>
              <div class="stat-label">钓鱼邮件</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon size="40" color="#67C23A"><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats?.safe_count || 0 }}</div>
              <div class="stat-label">安全邮件</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon">
              <el-icon size="40" color="#E6A23C"><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ ((stats?.phishing_rate || 0) * 100).toFixed(1) }}%</div>
              <div class="stat-label">钓鱼率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 风险等级分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>风险等级分布</span>
          </template>
          <div class="chart-container">
            <v-chart :option="riskDistributionOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>

      <!-- 检测趋势 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>检测趋势</span>
          </template>
          <div class="chart-container">
            <v-chart :option="trendOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 性能指标 -->
    <el-row :gutter="20" class="performance-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>性能指标</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="平均置信度">
              <el-progress 
                :percentage="Math.round((stats?.avg_confidence || 0) * 100)"
                :color="getConfidenceColor(stats?.avg_confidence || 0)"
              />
              <span style="margin-left: 10px;">{{ ((stats?.avg_confidence || 0) * 100).toFixed(1) }}%</span>
            </el-descriptions-item>
            <el-descriptions-item label="平均处理时间">
              {{ (stats?.avg_processing_time || 0).toFixed(3) }} 秒
            </el-descriptions-item>
            <el-descriptions-item label="检测准确率">
              <el-tag type="success">95.2%</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="系统状态">
              <el-tag type="success">正常运行</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <!-- 最近检测记录 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>最近检测记录</span>
              <el-button type="text" @click="$router.push('/history')">查看全部</el-button>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="item in recentHistory"
              :key="item.id"
              :timestamp="formatDateTime(item.detection_time)"
              placement="top"
            >
              <div class="timeline-item">
                <div class="timeline-filename">{{ item.filename }}</div>
                <el-tag :type="item.is_phishing ? 'danger' : 'success'" size="small">
                  {{ item.is_phishing ? '钓鱼邮件' : '安全邮件' }}
                </el-tag>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useDetectionStore } from '../stores/detection'

const detectionStore = useDetectionStore()

const selectedDays = ref(30)
const stats = ref(null)
const recentHistory = ref([])

onMounted(() => {
  loadData()
})

const loadData = async () => {
  try {
    // 加载统计数据
    stats.value = await detectionStore.getStatistics(selectedDays.value)
    
    // 加载最近的检测记录
    const historyResponse = await detectionStore.getHistory({ page: 1, page_size: 5 })
    recentHistory.value = historyResponse.results
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

const refreshData = () => {
  loadData()
}

const handleTimeChange = () => {
  loadData()
}

// 风险等级分布图表配置
const riskDistributionOption = computed(() => ({
  title: {
    text: '风险等级分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '风险等级',
      type: 'pie',
      radius: '50%',
      data: [
        { value: stats.value?.risk_level_distribution?.safe || 0, name: '安全', itemStyle: { color: '#67C23A' } },
        { value: stats.value?.risk_level_distribution?.low || 0, name: '低危', itemStyle: { color: '#409EFF' } },
        { value: stats.value?.risk_level_distribution?.medium || 0, name: '中危', itemStyle: { color: '#E6A23C' } },
        { value: stats.value?.risk_level_distribution?.high || 0, name: '高危', itemStyle: { color: '#F56C6C' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 检测趋势图表配置
const trendOption = computed(() => ({
  title: {
    text: '每日检测趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: stats.value?.daily_detections?.map(item => item.date) || []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '检测数量',
      type: 'line',
      data: stats.value?.daily_detections?.map(item => item.count) || [],
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      },
      areaStyle: {
        color: 'rgba(64, 158, 255, 0.1)'
      }
    }
  ]
}))

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}
</script>

<style scoped>
.statistics {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.time-selector {
  margin-bottom: 20px;
}

.time-controls {
  display: flex;
  align-items: center;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 15px;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #333;
}

.stat-label {
  color: #666;
  font-size: 0.9em;
}

.charts-row {
  margin-bottom: 20px;
}

.charts-row .el-card {
  height: 400px;
}

.chart-container {
  padding: 10px;
  height: calc(100% - 60px);
}

.performance-row {
  margin-bottom: 20px;
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeline-filename {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 10px;
}
</style>
