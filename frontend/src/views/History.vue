<template>
  <div class="history">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>检测历史</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filters">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filters.risk_level" placeholder="风险等级" clearable @change="handleFilterChange">
              <el-option label="安全" value="safe" />
              <el-option label="低危" value="low" />
              <el-option label="中危" value="medium" />
              <el-option label="高危" value="high" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.is_phishing" placeholder="是否钓鱼邮件" clearable @change="handleFilterChange">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input v-model="searchKeyword" placeholder="搜索文件名" clearable @input="handleSearch">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="filteredHistory"
        v-loading="detectionStore.loading"
        stripe
        style="width: 100%"
        :expand-row-keys="expandedRows"
        :row-key="getRowKey"
        @expand-change="handleExpandChange"
      >
        <!-- 展开列（仅批量检测显示） -->
        <el-table-column type="expand" width="50">
          <template #default="{ row }">
            <div v-if="row.type === 'batch'" class="batch-details">
              <div class="batch-summary">
                <h4>批量检测详情</h4>
                <el-descriptions :column="4" border size="small">
                  <el-descriptions-item label="总文件数">{{ row.data.total_files }}</el-descriptions-item>
                  <el-descriptions-item label="已处理">{{ row.data.processed_files }}</el-descriptions-item>
                  <el-descriptions-item label="钓鱼邮件">{{ row.data.phishing_count }}</el-descriptions-item>
                  <el-descriptions-item label="安全邮件">{{ row.data.safe_count }}</el-descriptions-item>
                  <el-descriptions-item label="平均置信度">{{ (row.data.avg_confidence * 100).toFixed(1) }}%</el-descriptions-item>
                  <el-descriptions-item label="检测模式">{{ row.data.detection_mode_display }}</el-descriptions-item>
                  <el-descriptions-item label="开始时间">{{ row.data.start_time }}</el-descriptions-item>
                  <el-descriptions-item label="结束时间">{{ row.data.end_time }}</el-descriptions-item>
                </el-descriptions>
              </div>

              <div v-if="batchDetails[row.data.id]" class="batch-results">
                <h5>详细检测结果</h5>
                <el-table :data="batchDetails[row.data.id].detection_results" size="small" max-height="400">
                  <el-table-column prop="email_file.filename" label="文件名" min-width="200" />
                  <el-table-column prop="is_phishing" label="检测结果" width="100">
                    <template #default="{ row: detailRow }">
                      <el-tag :type="detailRow.is_phishing ? 'danger' : 'success'" size="small">
                        {{ detailRow.is_phishing ? '钓鱼' : '安全' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="confidence" label="置信度" width="100">
                    <template #default="{ row: detailRow }">
                      {{ (detailRow.confidence * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                  <el-table-column prop="risk_level" label="风险等级" width="100">
                    <template #default="{ row: detailRow }">
                      <el-tag :type="getRiskTagType(detailRow.risk_level)" size="small">
                        {{ detailRow.risk_level_display }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="processing_time" label="处理时间" width="100">
                    <template #default="{ row: detailRow }">
                      {{ detailRow.processing_time.toFixed(3) }}s
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100">
                    <template #default="{ row: detailRow }">
                      <el-button type="primary" size="small" @click="viewDetail(detailRow.id)">
                        详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div v-else class="loading-details">
                <el-button type="primary" @click="loadBatchDetails(row.data.id)" :loading="loadingBatchDetails[row.data.id]">
                  加载详细结果
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 类型/文件名列 -->
        <el-table-column label="类型/文件名" min-width="250">
          <template #default="{ row }">
            <div v-if="row.type === 'batch'" class="batch-row">
              <div class="batch-header">
                <el-icon class="batch-icon"><FolderOpened /></el-icon>
                <span class="batch-name">{{ row.data.name }}</span>
                <el-tag type="info" size="small">批量检测</el-tag>
              </div>
              <div class="batch-info">
                <span class="file-count">{{ row.data.total_files }} 个文件</span>
                <span class="separator">•</span>
                <span class="phishing-count">{{ row.data.phishing_count }} 个钓鱼邮件</span>
              </div>
            </div>
            <div v-else class="single-row">
              <el-icon class="file-icon"><Document /></el-icon>
              <el-tooltip :content="row.data.filename" placement="top">
                <span class="filename">{{ row.data.filename }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <!-- 创建/上传时间 -->
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            <span v-if="row.type === 'batch'">
              {{ formatDateTime(row.data.created_time) }}
            </span>
            <span v-else>
              {{ formatDateTime(row.data.upload_time) }}
            </span>
          </template>
        </el-table-column>

        <!-- 状态/风险等级 -->
        <el-table-column label="状态/风险" width="120">
          <template #default="{ row }">
            <div v-if="row.type === 'batch'">
              <el-tag :type="getBatchStatusType(row.data.status)" size="small">
                {{ row.data.status_display }}
              </el-tag>
              <div v-if="row.data.status === 'processing'" class="progress-info">
                <el-progress
                  :percentage="row.data.progress_percentage"
                  :stroke-width="4"
                  :show-text="false"
                />
                <span class="progress-text">{{ row.data.progress_percentage }}%</span>
              </div>
            </div>
            <div v-else>
              <el-tag :type="getRiskTagType(row.data.risk_level)">
                {{ row.data.risk_level_display }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 检测结果 -->
        <el-table-column label="检测结果" width="150">
          <template #default="{ row }">
            <div v-if="row.type === 'batch'">
              <div class="batch-result-summary">
                <div class="result-item">
                  <el-tag type="danger" size="small">钓鱼: {{ row.data.phishing_count }}</el-tag>
                </div>
                <div class="result-item">
                  <el-tag type="success" size="small">安全: {{ row.data.safe_count }}</el-tag>
                </div>
              </div>
            </div>
            <div v-else>
              <el-tag :type="row.data.is_phishing ? 'danger' : 'success'">
                {{ row.data.is_phishing ? '钓鱼邮件' : '安全邮件' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 置信度 -->
        <el-table-column label="置信度" width="120">
          <template #default="{ row }">
            <div v-if="row.type === 'batch'">
              <el-progress
                :percentage="Math.round(row.data.avg_confidence * 100)"
                :color="getConfidenceColor(row.data.avg_confidence)"
                :stroke-width="6"
                text-inside
                :show-text="false"
              />
              <span class="confidence-text">{{ (row.data.avg_confidence * 100).toFixed(1) }}%</span>
            </div>
            <div v-else>
              <el-progress
                :percentage="Math.round(row.data.confidence * 100)"
                :color="getConfidenceColor(row.data.confidence)"
                :stroke-width="6"
                text-inside
                :show-text="false"
              />
              <span class="confidence-text">{{ (row.data.confidence * 100).toFixed(1) }}%</span>
            </div>
          </template>
        </el-table-column>

        <!-- 检测模式 -->
        <el-table-column label="检测模式" width="120">
          <template #default="{ row }">
            <span v-if="row.type === 'batch'">
              {{ row.data.detection_mode_display || '标准检测' }}
            </span>
            <span v-else>
              标准检测
            </span>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div v-if="row.type === 'batch'">
              <el-button type="primary" size="small" @click="viewBatchSummary(row.data)">
                查看汇总
              </el-button>
            </div>
            <div v-else>
              <el-button type="primary" size="small" @click="viewDetail(row.data.id)">
                查看详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="检测详情"
      width="80%"
      :before-close="handleDetailClose"
    >
      <DetailAnalysis
        v-if="currentDetail"
        :result="currentDetail"
      />
    </el-dialog>

    <!-- 批量检测汇总对话框 -->
    <el-dialog
      v-model="showBatchSummaryDialog"
      title="批量检测汇总"
      width="70%"
      :before-close="handleBatchSummaryClose"
    >
      <div v-if="currentBatchSummary" class="batch-summary-content">
        <el-descriptions title="任务信息" :column="2" border>
          <el-descriptions-item label="任务名称">{{ currentBatchSummary.name }}</el-descriptions-item>
          <el-descriptions-item label="任务描述">{{ currentBatchSummary.description }}</el-descriptions-item>
          <el-descriptions-item label="检测模式">{{ currentBatchSummary.detection_mode_display }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getBatchStatusType(currentBatchSummary.status)">
              {{ currentBatchSummary.status_display }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentBatchSummary.created_time }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ currentBatchSummary.end_time || '未完成' }}</el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="检测统计" :column="4" border style="margin-top: 20px;">
          <el-descriptions-item label="总文件数">{{ currentBatchSummary.total_files }}</el-descriptions-item>
          <el-descriptions-item label="已处理">{{ currentBatchSummary.processed_files }}</el-descriptions-item>
          <el-descriptions-item label="处理进度">{{ currentBatchSummary.progress_percentage }}%</el-descriptions-item>
          <el-descriptions-item label="平均置信度">{{ (currentBatchSummary.avg_confidence * 100).toFixed(1) }}%</el-descriptions-item>
        </el-descriptions>

        <div class="risk-distribution" style="margin-top: 20px;">
          <h4>风险等级分布</h4>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="高危邮件" :value="currentBatchSummary.high_risk_count">
                <template #suffix>
                  <el-tag type="danger" size="small">高危</el-tag>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="6">
              <el-statistic title="中危邮件" :value="currentBatchSummary.medium_risk_count">
                <template #suffix>
                  <el-tag type="warning" size="small">中危</el-tag>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="6">
              <el-statistic title="低危邮件" :value="currentBatchSummary.low_risk_count">
                <template #suffix>
                  <el-tag type="info" size="small">低危</el-tag>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="6">
              <el-statistic title="安全邮件" :value="currentBatchSummary.safe_risk_count">
                <template #suffix>
                  <el-tag type="success" size="small">安全</el-tag>
                </template>
              </el-statistic>
            </el-col>
          </el-row>
        </div>

        <div class="detection-results" style="margin-top: 20px;">
          <h4>检测结果分布</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-statistic title="钓鱼邮件" :value="currentBatchSummary.phishing_count">
                <template #suffix>
                  <span style="color: #f56c6c;">{{ currentBatchSummary.phishing_percentage }}%</span>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="12">
              <el-statistic title="安全邮件" :value="currentBatchSummary.safe_count">
                <template #suffix>
                  <span style="color: #67c23a;">{{ (100 - currentBatchSummary.phishing_percentage).toFixed(1) }}%</span>
                </template>
              </el-statistic>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { FolderOpened, Document, Refresh, Search } from '@element-plus/icons-vue'
import { useDetectionStore, type DetectionHistory, type DetectionResult } from '../stores/detection'
import DetailAnalysis from '../components/DetailAnalysis.vue'

const detectionStore = useDetectionStore()

const filters = ref({
  risk_level: '',
  is_phishing: null as boolean | null
})

const searchKeyword = ref('')
const showDetailDialog = ref(false)
const showBatchSummaryDialog = ref(false)
const currentDetail = ref<DetectionResult | null>(null)
const currentBatchSummary = ref<any>(null)

// 批量检测相关状态
const expandedRows = ref<string[]>([])
const batchDetails = ref<Record<string, any>>({})
const loadingBatchDetails = ref<Record<string, boolean>>({})

const pagination = ref({
  page: 1,
  pageSize: 20,
  total: 0
})

// 过滤后的历史记录
const filteredHistory = computed(() => {
  let result = detectionStore.history

  // 按文件名/任务名搜索
  if (searchKeyword.value) {
    result = result.filter(item => {
      if (item.type === 'batch') {
        return item.data.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
               item.data.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
      } else {
        return item.data.filename.toLowerCase().includes(searchKeyword.value.toLowerCase())
      }
    })
  }

  return result
})

onMounted(() => {
  loadHistory()
})

const loadHistory = async () => {
  try {
    const params = {
      page: pagination.value.page,
      page_size: pagination.value.pageSize,
      ...filters.value
    }
    
    const response = await detectionStore.getHistory(params)
    pagination.value.total = response.total_count
  } catch (error) {
    ElMessage.error('加载历史记录失败')
  }
}

const refreshData = () => {
  loadHistory()
}

const handleFilterChange = () => {
  pagination.value.page = 1
  loadHistory()
}

const handleSearch = () => {
  // 搜索在前端进行，不需要重新加载数据
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.page = 1
  loadHistory()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  loadHistory()
}

const viewDetail = async (resultId: string) => {
  try {
    currentDetail.value = await detectionStore.getDetectionResult(resultId)
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取详情失败')
  }
}

const handleDetailClose = () => {
  showDetailDialog.value = false
  currentDetail.value = null
}

// 批量检测相关方法
const handleExpandChange = (row: any, expandedRowsData: any[]) => {
  if (row.type === 'batch') {
    const isExpanded = expandedRowsData.some(r => r.data.id === row.data.id)
    if (isExpanded && !batchDetails.value[row.data.id]) {
      // 展开时自动加载详情
      loadBatchDetails(row.data.id)
    }
  }
}

const loadBatchDetails = async (batchId: string) => {
  try {
    loadingBatchDetails.value[batchId] = true
    const response = await fetch(`/api/detection/batch/detail/${batchId}/`)
    const data = await response.json()
    batchDetails.value[batchId] = data
  } catch (error) {
    ElMessage.error('加载批量检测详情失败')
  } finally {
    loadingBatchDetails.value[batchId] = false
  }
}

const viewBatchSummary = (batchData: any) => {
  currentBatchSummary.value = batchData
  showBatchSummaryDialog.value = true
}

const handleBatchSummaryClose = () => {
  showBatchSummaryDialog.value = false
  currentBatchSummary.value = null
}

// 获取行的唯一键
const getRowKey = (row: any) => {
  if (row.type === 'batch') {
    return `batch_${row.data.id}`
  } else {
    return `single_${row.data.id}`
  }
}

// 新的状态类型判断方法
const getBatchStatusType = (status: string) => {
  const types: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return types[status] || 'info'
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getRiskTagType = (riskLevel: string) => {
  const types: Record<string, string> = {
    'safe': 'success',
    'low': 'info',
    'medium': 'warning',
    'high': 'danger'
  }
  return types[riskLevel] || 'info'
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.8) return '#67c23a'
  if (confidence >= 0.6) return '#e6a23c'
  return '#f56c6c'
}
</script>

<style scoped>
.history {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filters {
  margin-bottom: 20px;
}

.filename {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.confidence-text {
  margin-left: 10px;
  font-size: 12px;
  color: #666;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 批量检测相关样式 */
.batch-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.batch-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-icon {
  color: #409eff;
  font-size: 16px;
}

.batch-name {
  font-weight: 500;
  color: #303133;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.separator {
  color: #dcdfe6;
}

.single-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #67c23a;
  font-size: 16px;
}

.batch-result-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-item {
  display: flex;
  align-items: center;
}

.progress-info {
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
}

/* 批量详情展开区域 */
.batch-details {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 10px 0;
}

.batch-summary h4 {
  margin-bottom: 16px;
  color: #303133;
}

.batch-results {
  margin-top: 20px;
}

.batch-results h5 {
  margin-bottom: 12px;
  color: #606266;
}

.loading-details {
  text-align: center;
  padding: 20px;
}

.batch-summary-content {
  padding: 20px;
}

.risk-distribution h4,
.detection-results h4 {
  margin-bottom: 16px;
  color: #303133;
  font-size: 16px;
}
</style>
