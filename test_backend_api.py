"""
测试后端API是否正常工作
"""

import requests
import json

def test_backend_connection():
    """测试后端连接"""
    try:
        # 测试基本连接
        response = requests.get('http://localhost:8000/api/statistics/')
        print(f"统计API状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 后端API连接正常")
            return True
        else:
            print(f"❌ 后端API连接失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 后端连接异常: {e}")
        return False

def test_batch_upload_endpoint():
    """测试批量上传端点"""
    try:
        # 创建一个简单的测试文件
        test_content = """Subject: Test Email
From: <EMAIL>
To: <EMAIL>

This is a test email for batch upload."""
        
        # 创建临时文件
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file = f.name
        
        try:
            # 测试上传
            with open(temp_file, 'rb') as f:
                files = {'files': f}
                response = requests.post('http://localhost:8000/api/batch/upload/', files=files)
            
            print(f"批量上传API状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 批量上传API正常")
                print(f"   批量ID: {data.get('batch_id', 'N/A')}")
                print(f"   文件数: {data.get('total_files', 'N/A')}")
                return True
            else:
                print(f"❌ 批量上传API失败: {response.text}")
                return False
                
        finally:
            # 清理临时文件
            os.unlink(temp_file)
            
    except Exception as e:
        print(f"❌ 批量上传测试异常: {e}")
        return False

def main():
    print("🔍 测试后端API...")
    print("=" * 40)
    
    # 测试基本连接
    if not test_backend_connection():
        print("\n❌ 后端服务未启动或配置错误")
        print("请确保运行: cd backend && python manage.py runserver")
        return
    
    print()
    
    # 测试批量上传端点
    if test_batch_upload_endpoint():
        print("\n✅ 所有API测试通过!")
        print("\n📋 解决方案:")
        print("1. 重启前端开发服务器以应用代理配置:")
        print("   cd frontend")
        print("   npm run dev")
        print("2. 确保前端访问: http://localhost:5173/batch-detection")
    else:
        print("\n❌ API测试失败")
        print("请检查后端代码和配置")

if __name__ == "__main__":
    main()
