"""
测试特征重要性功能
验证后端是否正确生成特征重要性数据
"""

import requests
import json

def test_feature_importance():
    """测试特征重要性功能"""
    
    # 测试邮件内容
    test_email = """Subject: Urgent Account Verification Required
From: <EMAIL>
To: <EMAIL>

Dear Customer,

Your account will be suspended unless you verify immediately!
Click here to verify: http://fake-bank.com/verify
Please provide your password and credit card details.

This is urgent and requires immediate action.

Bank Security Team
"""
    
    # 测试单邮件检测
    print("🧪 测试单邮件检测的特征重要性...")
    
    try:
        # 调用检测API
        response = requests.post(
            'http://localhost:8000/api/detect/',
            json={
                'email_content': test_email,
                'detection_mode': 'standard'
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 检测成功!")
            print(f"   检测结果: {'钓鱼邮件' if result['is_phishing'] else '正常邮件'}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   风险等级: {result['risk_level']}")
            
            # 检查特征重要性
            prediction_details = result.get('prediction_details', {})
            feature_importance = prediction_details.get('feature_importance', {})
            
            if feature_importance:
                print("\n📊 特征重要性分析:")
                for feature, importance in feature_importance.items():
                    print(f"   {feature}: {importance:.3f} ({importance*100:.1f}%)")
                
                # 验证总和是否接近1
                total = sum(feature_importance.values())
                print(f"\n   总和: {total:.3f} (应该接近1.0)")
                
                if abs(total - 1.0) < 0.01:
                    print("✅ 特征重要性归一化正确")
                else:
                    print("❌ 特征重要性归一化有问题")
                
                return True
            else:
                print("❌ 没有找到特征重要性数据")
                print("   prediction_details:", prediction_details)
                return False
                
        else:
            print(f"❌ 检测失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_history_detail():
    """测试历史记录详情中的特征重要性"""
    
    print("\n🧪 测试历史记录详情的特征重要性...")
    
    try:
        # 获取历史记录
        response = requests.get('http://localhost:8000/api/history/?page_size=1')
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            
            if results:
                record = results[0]
                record_id = record['id']
                
                print(f"   获取记录ID: {record_id}")
                
                # 获取详情
                detail_response = requests.get(f'http://localhost:8000/api/result/{record_id}/')
                
                if detail_response.status_code == 200:
                    detail = detail_response.json()
                    
                    prediction_details = detail.get('prediction_details', {})
                    feature_importance = prediction_details.get('feature_importance', {})
                    
                    if feature_importance:
                        print("✅ 历史记录详情包含特征重要性:")
                        for feature, importance in feature_importance.items():
                            print(f"   {feature}: {importance:.3f}")
                        return True
                    else:
                        print("❌ 历史记录详情缺少特征重要性")
                        print("   prediction_details:", prediction_details)
                        return False
                else:
                    print(f"❌ 获取详情失败: {detail_response.status_code}")
                    return False
            else:
                print("❌ 没有历史记录")
                return False
        else:
            print(f"❌ 获取历史记录失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试特征重要性功能\n")
    
    # 测试1: 单邮件检测
    test1_success = test_feature_importance()
    
    # 测试2: 历史记录详情
    test2_success = test_history_detail()
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   单邮件检测: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   历史记录详情: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过! 特征重要性功能正常工作")
        return True
    else:
        print("\n💥 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    main()
