"""
元学习优化模块
实现MAML（Model-Agnostic Meta-Learning）算法

创新点二的核心组件：
1. MAML元学习优化器：快速适应未知攻击模式
2. 元任务定义：将对抗样本作为"新类别"
3. 快速适应机制：少样本学习新的钓鱼攻击类型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import Adam, SGD
import numpy as np
from typing import List, Dict, Tuple, Optional, Callable
from collections import OrderedDict
import copy


class MAMLOptimizer:
    """
    MAML元学习优化器
    实现模型无关的元学习算法，使模型能够快速适应新的攻击模式
    """
    
    def __init__(self, model: nn.Module, inner_lr: float = 0.01, outer_lr: float = 0.001,
                 inner_steps: int = 5, device: str = 'cuda'):
        """
        初始化MAML优化器
        
        Args:
            model: 要进行元学习的模型
            inner_lr: 内循环学习率
            outer_lr: 外循环学习率  
            inner_steps: 内循环更新步数
            device: 设备
        """
        self.model = model
        self.inner_lr = inner_lr
        self.outer_lr = outer_lr
        self.inner_steps = inner_steps
        self.device = device
        
        # 外循环优化器
        self.meta_optimizer = Adam(self.model.parameters(), lr=outer_lr)
        
        # 损失函数
        self.loss_fn = nn.CrossEntropyLoss()
        
    def inner_update(self, support_x: torch.Tensor, support_y: torch.Tensor,
                    model_params: OrderedDict) -> OrderedDict:
        """
        内循环更新：在支持集上快速适应
        
        Args:
            support_x: 支持集输入
            support_y: 支持集标签
            model_params: 当前模型参数
            
        Returns:
            更新后的模型参数
        """
        # 使用当前参数计算损失
        logits = self._forward_with_params(support_x, model_params)
        loss = self.loss_fn(logits, support_y)
        
        # 计算梯度
        grads = torch.autograd.grad(loss, model_params.values(), create_graph=True)
        
        # 更新参数
        updated_params = OrderedDict()
        for (name, param), grad in zip(model_params.items(), grads):
            updated_params[name] = param - self.inner_lr * grad
            
        return updated_params
    
    def meta_update(self, tasks: List[Dict]) -> Dict[str, float]:
        """
        元更新：在多个任务上优化模型的初始化参数
        
        Args:
            tasks: 任务列表，每个任务包含support_x, support_y, query_x, query_y
            
        Returns:
            训练损失统计
        """
        self.meta_optimizer.zero_grad()
        
        total_loss = 0.0
        total_accuracy = 0.0
        
        for task in tasks:
            support_x = task['support_x'].to(self.device)
            support_y = task['support_y'].to(self.device)
            query_x = task['query_x'].to(self.device)
            query_y = task['query_y'].to(self.device)
            
            # 获取当前模型参数
            model_params = OrderedDict(self.model.named_parameters())
            
            # 内循环：在支持集上快速适应
            adapted_params = model_params
            for _ in range(self.inner_steps):
                adapted_params = self.inner_update(support_x, support_y, adapted_params)
            
            # 在查询集上计算损失
            query_logits = self._forward_with_params(query_x, adapted_params)
            query_loss = self.loss_fn(query_logits, query_y)
            
            total_loss += query_loss
            
            # 计算准确率
            pred = torch.argmax(query_logits, dim=1)
            accuracy = (pred == query_y).float().mean()
            total_accuracy += accuracy
        
        # 平均损失
        avg_loss = total_loss / len(tasks)
        avg_accuracy = total_accuracy / len(tasks)
        
        # 反向传播和更新
        avg_loss.backward()
        self.meta_optimizer.step()
        
        return {
            'meta_loss': avg_loss.item(),
            'meta_accuracy': avg_accuracy.item()
        }
    
    def _forward_with_params(self, x: torch.Tensor, params: OrderedDict) -> torch.Tensor:
        """
        使用指定参数进行前向传播
        
        Args:
            x: 输入数据
            params: 模型参数
            
        Returns:
            模型输出
        """
        # 这里需要根据具体模型结构实现
        # 示例实现（需要根据实际模型调整）
        return self.model.forward_with_params(x, params)
    
    def fast_adapt(self, support_x: torch.Tensor, support_y: torch.Tensor,
                  query_x: torch.Tensor, adaptation_steps: int = None) -> torch.Tensor:
        """
        快速适应新任务
        
        Args:
            support_x: 支持集输入
            support_y: 支持集标签
            query_x: 查询集输入
            adaptation_steps: 适应步数
            
        Returns:
            查询集预测结果
        """
        if adaptation_steps is None:
            adaptation_steps = self.inner_steps
            
        # 获取当前模型参数
        model_params = OrderedDict(self.model.named_parameters())
        
        # 在支持集上快速适应
        adapted_params = model_params
        for _ in range(adaptation_steps):
            adapted_params = self.inner_update(support_x, support_y, adapted_params)
        
        # 在查询集上预测
        with torch.no_grad():
            query_logits = self._forward_with_params(query_x, adapted_params)
            
        return query_logits


class MetaLearner:
    """
    元学习器
    管理元学习训练过程和任务生成
    """
    
    def __init__(self, base_model: nn.Module, maml_optimizer: MAMLOptimizer):
        """
        初始化元学习器
        
        Args:
            base_model: 基础模型
            maml_optimizer: MAML优化器
        """
        self.base_model = base_model
        self.maml_optimizer = maml_optimizer
        
    def create_meta_tasks(self, adversarial_samples: List[str], 
                         normal_samples: List[str],
                         task_size: int = 10, 
                         num_tasks: int = 20) -> List[Dict]:
        """
        创建元学习任务
        将对抗样本作为"新类别"加入训练
        
        Args:
            adversarial_samples: 对抗样本列表
            normal_samples: 正常样本列表
            task_size: 每个任务的样本数量
            num_tasks: 任务数量
            
        Returns:
            元学习任务列表
        """
        tasks = []
        
        for _ in range(num_tasks):
            # 随机采样对抗样本和正常样本
            task_adversarial = np.random.choice(adversarial_samples, task_size // 2, replace=False)
            task_normal = np.random.choice(normal_samples, task_size // 2, replace=False)
            
            # 创建标签（0: 正常, 1: 钓鱼）
            adversarial_labels = [1] * len(task_adversarial)
            normal_labels = [0] * len(task_normal)
            
            # 合并数据
            task_texts = list(task_adversarial) + list(task_normal)
            task_labels = adversarial_labels + normal_labels
            
            # 划分支持集和查询集
            support_size = len(task_texts) // 2
            indices = np.random.permutation(len(task_texts))
            
            support_indices = indices[:support_size]
            query_indices = indices[support_size:]
            
            support_texts = [task_texts[i] for i in support_indices]
            support_labels = [task_labels[i] for i in support_indices]
            query_texts = [task_texts[i] for i in query_indices]
            query_labels = [task_labels[i] for i in query_indices]
            
            # 转换为张量（这里需要根据实际的特征提取方法调整）
            support_x = self._texts_to_tensor(support_texts)
            support_y = torch.tensor(support_labels, dtype=torch.long)
            query_x = self._texts_to_tensor(query_texts)
            query_y = torch.tensor(query_labels, dtype=torch.long)
            
            tasks.append({
                'support_x': support_x,
                'support_y': support_y,
                'query_x': query_x,
                'query_y': query_y
            })
        
        return tasks
    
    def _texts_to_tensor(self, texts: List[str]) -> torch.Tensor:
        """
        将文本转换为张量
        这里需要根据实际的特征提取方法实现
        
        Args:
            texts: 文本列表
            
        Returns:
            特征张量
        """
        # 示例实现：随机特征（实际应该使用真实的特征提取）
        features = []
        for text in texts:
            # 这里应该调用实际的特征提取器
            feature = torch.randn(768)  # 假设特征维度为768
            features.append(feature)
        
        return torch.stack(features)
    
    def train_meta_learning(self, adversarial_samples: List[str],
                           normal_samples: List[str],
                           num_epochs: int = 100,
                           tasks_per_epoch: int = 20) -> List[Dict]:
        """
        训练元学习模型
        
        Args:
            adversarial_samples: 对抗样本
            normal_samples: 正常样本
            num_epochs: 训练轮数
            tasks_per_epoch: 每轮任务数
            
        Returns:
            训练历史
        """
        training_history = []
        
        for epoch in range(num_epochs):
            # 创建元学习任务
            tasks = self.create_meta_tasks(
                adversarial_samples, normal_samples, 
                num_tasks=tasks_per_epoch
            )
            
            # 元更新
            metrics = self.maml_optimizer.meta_update(tasks)
            metrics['epoch'] = epoch
            
            training_history.append(metrics)
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch}: Meta Loss = {metrics['meta_loss']:.4f}, "
                      f"Meta Accuracy = {metrics['meta_accuracy']:.4f}")
        
        return training_history
    
    def evaluate_zero_shot(self, new_attack_samples: List[str],
                          normal_samples: List[str],
                          adaptation_steps: int = 5) -> Dict[str, float]:
        """
        评估零样本检测性能
        
        Args:
            new_attack_samples: 新型攻击样本
            normal_samples: 正常样本
            adaptation_steps: 适应步数
            
        Returns:
            评估指标
        """
        # 创建少量支持样本用于快速适应
        support_size = min(5, len(new_attack_samples))
        support_attacks = new_attack_samples[:support_size]
        support_normals = normal_samples[:support_size]
        
        # 创建查询集
        query_attacks = new_attack_samples[support_size:]
        query_normals = normal_samples[support_size:len(query_attacks) + support_size]
        
        # 准备数据
        support_texts = support_attacks + support_normals
        support_labels = [1] * len(support_attacks) + [0] * len(support_normals)
        
        query_texts = query_attacks + query_normals
        query_labels = [1] * len(query_attacks) + [0] * len(query_normals)
        
        # 转换为张量
        support_x = self._texts_to_tensor(support_texts)
        support_y = torch.tensor(support_labels, dtype=torch.long)
        query_x = self._texts_to_tensor(query_texts)
        query_y = torch.tensor(query_labels, dtype=torch.long)
        
        # 快速适应
        query_logits = self.maml_optimizer.fast_adapt(
            support_x, support_y, query_x, adaptation_steps
        )
        
        # 计算准确率
        pred = torch.argmax(query_logits, dim=1)
        accuracy = (pred == query_y).float().mean().item()
        
        # 计算精确率和召回率
        tp = ((pred == 1) & (query_y == 1)).sum().item()
        fp = ((pred == 1) & (query_y == 0)).sum().item()
        fn = ((pred == 0) & (query_y == 1)).sum().item()
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score
        }
