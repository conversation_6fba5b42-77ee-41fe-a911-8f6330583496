"""
零样本钓鱼邮件检测器
整合对抗增强学习机制的完整检测系统

创新点二的集成模块：
1. 整合GAN对抗样本生成
2. 整合MAML元学习优化
3. 整合动态阈值机制
4. 提供统一的零样本检测接口
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import networkx as nx
import logging
from pathlib import Path

from .adversarial_learning import AdversarialGenerator, AdversarialTrainer
from .meta_learning import MAMLOptimizer, MetaLearner
from .dynamic_threshold import DynamicThresholdManager
from .gcn_model import MultiModalGCN
from .feature_extractors import TextFeatureExtractor, URLFeatureExtractor, MetadataFeatureExtractor


class ZeroShotPhishingDetector:
    """
    零样本钓鱼邮件检测器
    实现完整的对抗增强学习机制
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化零样本检测器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化特征提取器
        self.text_extractor = TextFeatureExtractor(config.get('text_config', {}))
        self.url_extractor = URLFeatureExtractor(config.get('url_config', {}))
        self.metadata_extractor = MetadataFeatureExtractor(config.get('metadata_config', {}))
        
        # 初始化GCN模型
        self.gcn_model = MultiModalGCN(config.get('gcn_config', {}))
        
        # 初始化对抗学习组件
        self.adversarial_generator = AdversarialGenerator(
            vocab_size=config.get('vocab_size', 30000),
            embedding_dim=config.get('embedding_dim', 768),
            hidden_dim=config.get('hidden_dim', 512)
        )
        self.adversarial_trainer = AdversarialTrainer(
            self.adversarial_generator, self.device
        )
        
        # 初始化元学习组件
        self.maml_optimizer = MAMLOptimizer(
            model=self.gcn_model,
            inner_lr=config.get('inner_lr', 0.01),
            outer_lr=config.get('outer_lr', 0.001),
            inner_steps=config.get('inner_steps', 5),
            device=self.device
        )
        self.meta_learner = MetaLearner(self.gcn_model, self.maml_optimizer)
        
        # 初始化动态阈值管理器
        self.threshold_manager = DynamicThresholdManager(
            base_threshold=config.get('base_threshold', 0.5),
            sensitivity=config.get('sensitivity', 0.3),
            adaptation_rate=config.get('adaptation_rate', 0.1)
        )
        
        # 训练状态
        self.is_trained = False
        self.training_history = []
        
    def extract_email_features(self, email_content: str, 
                             email_metadata: Dict) -> Dict[str, Any]:
        """
        提取邮件的多模态特征
        
        Args:
            email_content: 邮件内容
            email_metadata: 邮件元数据
            
        Returns:
            特征字典
        """
        features = {}
        
        # 文本特征
        features['text'] = self.text_extractor.extract(email_content)
        
        # URL特征
        urls = self.url_extractor.extract_urls(email_content)
        features['url'] = self.url_extractor.extract(urls)
        
        # 元数据特征
        features['metadata'] = self.metadata_extractor.extract(email_metadata)
        
        return features
    
    def build_email_graph(self, features: Dict[str, Any]) -> nx.Graph:
        """
        构建邮件的异构图结构
        
        Args:
            features: 邮件特征
            
        Returns:
            邮件图结构
        """
        graph = nx.Graph()
        
        # 添加邮件节点
        graph.add_node('email', type='email', features=features['text'])
        
        # 添加URL节点
        for i, url_feature in enumerate(features['url']):
            url_node = f'url_{i}'
            graph.add_node(url_node, type='url', features=url_feature)
            graph.add_edge('email', url_node, relation='contains')
        
        # 添加元数据节点
        for key, value in features['metadata'].items():
            meta_node = f'meta_{key}'
            graph.add_node(meta_node, type='metadata', features=value)
            graph.add_edge('email', meta_node, relation='has_metadata')
        
        return graph
    
    def generate_adversarial_samples(self, normal_emails: List[str],
                                   num_samples: int = 100) -> List[str]:
        """
        生成对抗样本
        
        Args:
            normal_emails: 正常邮件列表
            num_samples: 生成样本数量
            
        Returns:
            对抗样本列表
        """
        self.logger.info(f"Generating {num_samples} adversarial samples...")
        
        adversarial_samples = self.adversarial_trainer.generate_adversarial_samples(
            normal_emails, 
            tokenizer=self.text_extractor.tokenizer,
            model=self.text_extractor.model,
            num_samples=num_samples // len(normal_emails) + 1
        )
        
        return adversarial_samples[:num_samples]
    
    def train_meta_learning(self, training_emails: List[Dict],
                           num_epochs: int = 100) -> List[Dict]:
        """
        训练元学习模型
        
        Args:
            training_emails: 训练邮件数据
            num_epochs: 训练轮数
            
        Returns:
            训练历史
        """
        self.logger.info("Starting meta-learning training...")
        
        # 分离正常和钓鱼邮件
        normal_emails = [email['content'] for email in training_emails 
                        if email['label'] == 0]
        phishing_emails = [email['content'] for email in training_emails 
                          if email['label'] == 1]
        
        # 生成对抗样本
        adversarial_samples = self.generate_adversarial_samples(
            normal_emails, num_samples=len(phishing_emails)
        )
        
        # 训练元学习
        self.training_history = self.meta_learner.train_meta_learning(
            adversarial_samples=adversarial_samples,
            normal_samples=normal_emails,
            num_epochs=num_epochs
        )
        
        self.is_trained = True
        self.logger.info("Meta-learning training completed")
        
        return self.training_history
    
    def detect_zero_shot(self, email_content: str, 
                        email_metadata: Dict,
                        adaptation_samples: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        零样本检测
        
        Args:
            email_content: 邮件内容
            email_metadata: 邮件元数据
            adaptation_samples: 可选的适应样本
            
        Returns:
            检测结果
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before detection")
        
        # 提取特征
        features = self.extract_email_features(email_content, email_metadata)
        
        # 构建图结构
        email_graph = self.build_email_graph(features)
        
        # 如果有适应样本，进行快速适应
        if adaptation_samples:
            self._fast_adapt(adaptation_samples)
        
        # 使用GCN模型预测
        with torch.no_grad():
            # 这里需要将图结构转换为模型输入
            model_input = self._prepare_model_input(email_graph, features)
            prediction_logits = self.gcn_model(model_input)
            prediction_score = torch.softmax(prediction_logits, dim=-1)[0, 1].item()
        
        # 动态阈值决策
        is_phishing, threshold, diagnostics = self.threshold_manager.make_adaptive_decision(
            prediction_score, email_graph, features
        )
        
        # 构建检测结果
        result = {
            'is_phishing': is_phishing,
            'confidence': prediction_score,
            'threshold': threshold,
            'risk_level': self._determine_risk_level(prediction_score, threshold),
            'features': features,
            'graph_info': {
                'num_nodes': email_graph.number_of_nodes(),
                'num_edges': email_graph.number_of_edges(),
                'density': nx.density(email_graph)
            },
            'diagnostics': diagnostics
        }
        
        return result
    
    def _fast_adapt(self, adaptation_samples: List[Dict]):
        """
        快速适应新的攻击模式
        
        Args:
            adaptation_samples: 适应样本
        """
        if len(adaptation_samples) < 2:
            return
        
        # 准备适应数据
        support_texts = [sample['content'] for sample in adaptation_samples[:len(adaptation_samples)//2]]
        support_labels = [sample['label'] for sample in adaptation_samples[:len(adaptation_samples)//2]]
        
        query_texts = [sample['content'] for sample in adaptation_samples[len(adaptation_samples)//2:]]
        
        # 转换为张量
        support_x = self.meta_learner._texts_to_tensor(support_texts)
        support_y = torch.tensor(support_labels, dtype=torch.long)
        query_x = self.meta_learner._texts_to_tensor(query_texts)
        
        # 快速适应
        self.maml_optimizer.fast_adapt(support_x, support_y, query_x)
    
    def _prepare_model_input(self, graph: nx.Graph, features: Dict) -> torch.Tensor:
        """
        准备模型输入
        
        Args:
            graph: 邮件图
            features: 特征字典
            
        Returns:
            模型输入张量
        """
        # 这里需要根据实际的GCN模型输入格式调整
        # 简化实现：将特征拼接
        text_features = torch.tensor(features['text'], dtype=torch.float32)
        
        # 如果有多个特征模态，需要进行融合
        if len(features['url']) > 0:
            url_features = torch.tensor(features['url'][0], dtype=torch.float32)
            text_features = torch.cat([text_features, url_features])
        
        return text_features.unsqueeze(0)  # 添加batch维度
    
    def _determine_risk_level(self, confidence: float, threshold: float) -> str:
        """
        确定风险等级
        
        Args:
            confidence: 置信度
            threshold: 阈值
            
        Returns:
            风险等级
        """
        if confidence < threshold:
            return 'safe'
        elif confidence < threshold + 0.2:
            return 'low'
        elif confidence < threshold + 0.4:
            return 'medium'
        else:
            return 'high'
    
    def evaluate_zero_shot_performance(self, test_emails: List[Dict]) -> Dict[str, float]:
        """
        评估零样本检测性能
        
        Args:
            test_emails: 测试邮件数据
            
        Returns:
            性能指标
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        predictions = []
        true_labels = []
        
        for email in test_emails:
            result = self.detect_zero_shot(
                email['content'], 
                email.get('metadata', {})
            )
            
            predictions.append(1 if result['is_phishing'] else 0)
            true_labels.append(email['label'])
        
        # 计算性能指标
        predictions = np.array(predictions)
        true_labels = np.array(true_labels)
        
        tp = np.sum((predictions == 1) & (true_labels == 1))
        fp = np.sum((predictions == 1) & (true_labels == 0))
        tn = np.sum((predictions == 0) & (true_labels == 0))
        fn = np.sum((predictions == 0) & (true_labels == 1))
        
        accuracy = (tp + tn) / len(true_labels)
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'true_positives': int(tp),
            'false_positives': int(fp),
            'true_negatives': int(tn),
            'false_negatives': int(fn)
        }
    
    def save_model(self, save_path: str):
        """保存模型"""
        save_path = Path(save_path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # 保存各个组件
        torch.save(self.gcn_model.state_dict(), save_path / 'gcn_model.pth')
        torch.save(self.adversarial_generator.state_dict(), save_path / 'adversarial_generator.pth')
        
        # 保存配置和训练历史
        torch.save({
            'config': self.config,
            'training_history': self.training_history,
            'is_trained': self.is_trained
        }, save_path / 'detector_state.pth')
        
        self.logger.info(f"Model saved to {save_path}")
    
    def load_model(self, load_path: str):
        """加载模型"""
        load_path = Path(load_path)
        
        # 加载模型权重
        self.gcn_model.load_state_dict(torch.load(load_path / 'gcn_model.pth'))
        self.adversarial_generator.load_state_dict(torch.load(load_path / 'adversarial_generator.pth'))
        
        # 加载状态
        state = torch.load(load_path / 'detector_state.pth')
        self.training_history = state['training_history']
        self.is_trained = state['is_trained']
        
        self.logger.info(f"Model loaded from {load_path}")
