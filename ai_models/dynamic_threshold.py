"""
动态阈值调整模块
实现基于图结构异常度的动态阈值机制

创新点二的核心组件：
1. 节点异常度计算：基于图结构特征
2. 动态阈值调整：根据邮件图结构的异常程度
3. 新型攻击敏感性：提升对未知攻击模式的检测能力
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
import networkx as nx
from scipy import stats
import logging


class GraphAnomalyDetector:
    """
    图异常检测器
    计算图结构中的异常度指标
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def compute_node_anomaly_scores(self, graph: nx.Graph, 
                                   node_features: Dict) -> Dict[str, float]:
        """
        计算节点异常度分数
        
        Args:
            graph: 网络图
            node_features: 节点特征字典
            
        Returns:
            节点异常度分数字典
        """
        anomaly_scores = {}
        
        # 计算图结构特征
        degree_centrality = nx.degree_centrality(graph)
        betweenness_centrality = nx.betweenness_centrality(graph)
        clustering_coefficient = nx.clustering(graph)
        
        # 计算异常度
        for node in graph.nodes():
            # 度中心性异常
            degree_anomaly = self._compute_statistical_anomaly(
                degree_centrality[node], 
                list(degree_centrality.values())
            )
            
            # 介数中心性异常
            betweenness_anomaly = self._compute_statistical_anomaly(
                betweenness_centrality[node],
                list(betweenness_centrality.values())
            )
            
            # 聚类系数异常
            clustering_anomaly = self._compute_statistical_anomaly(
                clustering_coefficient[node],
                list(clustering_coefficient.values())
            )
            
            # 综合异常度分数
            anomaly_scores[node] = (
                0.4 * degree_anomaly + 
                0.3 * betweenness_anomaly + 
                0.3 * clustering_anomaly
            )
        
        return anomaly_scores
    
    def _compute_statistical_anomaly(self, value: float, 
                                   distribution: List[float]) -> float:
        """
        计算统计异常度
        
        Args:
            value: 当前值
            distribution: 分布数据
            
        Returns:
            异常度分数 (0-1)
        """
        if len(distribution) < 2:
            return 0.0
            
        # 计算Z分数
        mean_val = np.mean(distribution)
        std_val = np.std(distribution)
        
        if std_val == 0:
            return 0.0
            
        z_score = abs((value - mean_val) / std_val)
        
        # 将Z分数转换为0-1的异常度分数
        anomaly_score = min(z_score / 3.0, 1.0)  # 3个标准差外认为是异常
        
        return anomaly_score
    
    def detect_sudden_density_changes(self, graph: nx.Graph, 
                                    time_window: int = 5) -> float:
        """
        检测突发性高密度链接
        
        Args:
            graph: 网络图
            time_window: 时间窗口
            
        Returns:
            密度变化异常度
        """
        # 计算图密度
        current_density = nx.density(graph)
        
        # 这里应该维护历史密度数据
        # 简化实现：假设正常密度范围
        normal_density_range = (0.1, 0.3)
        
        if current_density < normal_density_range[0]:
            return 0.0
        elif current_density > normal_density_range[1]:
            # 密度过高，可能是攻击
            excess_density = current_density - normal_density_range[1]
            return min(excess_density / (1.0 - normal_density_range[1]), 1.0)
        else:
            return 0.0


class DynamicThresholdManager:
    """
    动态阈值管理器
    根据图异常度动态调整分类阈值
    """
    
    def __init__(self, base_threshold: float = 0.5, 
                 sensitivity: float = 0.3,
                 adaptation_rate: float = 0.1):
        """
        初始化动态阈值管理器
        
        Args:
            base_threshold: 基础阈值
            sensitivity: 敏感度参数
            adaptation_rate: 适应速率
        """
        self.base_threshold = base_threshold
        self.sensitivity = sensitivity
        self.adaptation_rate = adaptation_rate
        
        self.current_threshold = base_threshold
        self.anomaly_detector = GraphAnomalyDetector()
        
        # 历史数据
        self.threshold_history = [base_threshold]
        self.anomaly_history = []
        
        self.logger = logging.getLogger(__name__)
    
    def update_threshold(self, graph: nx.Graph, 
                        node_features: Dict,
                        prediction_confidence: float) -> float:
        """
        更新动态阈值
        
        Args:
            graph: 邮件图结构
            node_features: 节点特征
            prediction_confidence: 模型预测置信度
            
        Returns:
            更新后的阈值
        """
        # 计算图异常度
        graph_anomaly_score = self._compute_graph_anomaly(graph, node_features)
        
        # 根据异常度调整阈值
        threshold_adjustment = self._calculate_threshold_adjustment(
            graph_anomaly_score, prediction_confidence
        )
        
        # 更新阈值
        new_threshold = self.current_threshold + self.adaptation_rate * threshold_adjustment
        new_threshold = max(0.1, min(0.9, new_threshold))  # 限制阈值范围
        
        self.current_threshold = new_threshold
        self.threshold_history.append(new_threshold)
        self.anomaly_history.append(graph_anomaly_score)
        
        self.logger.info(f"Updated threshold: {new_threshold:.3f}, "
                        f"Graph anomaly: {graph_anomaly_score:.3f}")
        
        return new_threshold
    
    def _compute_graph_anomaly(self, graph: nx.Graph, 
                              node_features: Dict) -> float:
        """
        计算图整体异常度
        
        Args:
            graph: 网络图
            node_features: 节点特征
            
        Returns:
            图异常度分数
        """
        # 节点异常度
        node_anomalies = self.anomaly_detector.compute_node_anomaly_scores(
            graph, node_features
        )
        avg_node_anomaly = np.mean(list(node_anomalies.values()))
        
        # 密度异常度
        density_anomaly = self.anomaly_detector.detect_sudden_density_changes(graph)
        
        # 综合异常度
        graph_anomaly = 0.7 * avg_node_anomaly + 0.3 * density_anomaly
        
        return graph_anomaly
    
    def _calculate_threshold_adjustment(self, graph_anomaly: float,
                                      prediction_confidence: float) -> float:
        """
        计算阈值调整量
        
        Args:
            graph_anomaly: 图异常度
            prediction_confidence: 预测置信度
            
        Returns:
            阈值调整量
        """
        # 异常度越高，阈值应该降低（更敏感）
        anomaly_adjustment = -self.sensitivity * graph_anomaly
        
        # 预测置信度低时，也应该降低阈值
        confidence_adjustment = -0.1 * (1.0 - prediction_confidence)
        
        total_adjustment = anomaly_adjustment + confidence_adjustment
        
        return total_adjustment
    
    def get_adaptive_threshold(self, email_graph: nx.Graph,
                             email_features: Dict,
                             model_prediction: float) -> Tuple[float, Dict]:
        """
        获取自适应阈值
        
        Args:
            email_graph: 邮件图结构
            email_features: 邮件特征
            model_prediction: 模型预测分数
            
        Returns:
            (调整后的阈值, 诊断信息)
        """
        # 更新阈值
        new_threshold = self.update_threshold(
            email_graph, email_features, model_prediction
        )
        
        # 计算诊断信息
        graph_anomaly = self._compute_graph_anomaly(email_graph, email_features)
        
        diagnostics = {
            'threshold': new_threshold,
            'base_threshold': self.base_threshold,
            'graph_anomaly': graph_anomaly,
            'threshold_adjustment': new_threshold - self.base_threshold,
            'prediction_confidence': model_prediction
        }
        
        return new_threshold, diagnostics
    
    def make_adaptive_decision(self, model_prediction: float,
                             email_graph: nx.Graph,
                             email_features: Dict) -> Tuple[bool, float, Dict]:
        """
        基于动态阈值做出检测决策
        
        Args:
            model_prediction: 模型预测分数
            email_graph: 邮件图结构
            email_features: 邮件特征
            
        Returns:
            (是否为钓鱼邮件, 使用的阈值, 诊断信息)
        """
        # 获取自适应阈值
        adaptive_threshold, diagnostics = self.get_adaptive_threshold(
            email_graph, email_features, model_prediction
        )
        
        # 做出决策
        is_phishing = model_prediction > adaptive_threshold
        
        # 更新诊断信息
        diagnostics.update({
            'model_prediction': model_prediction,
            'is_phishing': is_phishing,
            'decision_margin': abs(model_prediction - adaptive_threshold)
        })
        
        return is_phishing, adaptive_threshold, diagnostics
    
    def reset_threshold(self):
        """重置阈值到基础值"""
        self.current_threshold = self.base_threshold
        self.threshold_history = [self.base_threshold]
        self.anomaly_history = []
        
        self.logger.info("Threshold reset to base value")
    
    def get_threshold_statistics(self) -> Dict:
        """
        获取阈值统计信息
        
        Returns:
            阈值统计字典
        """
        if len(self.threshold_history) == 0:
            return {}
            
        return {
            'current_threshold': self.current_threshold,
            'base_threshold': self.base_threshold,
            'mean_threshold': np.mean(self.threshold_history),
            'std_threshold': np.std(self.threshold_history),
            'min_threshold': np.min(self.threshold_history),
            'max_threshold': np.max(self.threshold_history),
            'threshold_range': np.max(self.threshold_history) - np.min(self.threshold_history),
            'num_adjustments': len(self.threshold_history) - 1
        }
