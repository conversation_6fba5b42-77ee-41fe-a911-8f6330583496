"""
特征提取器模块
实现多模态特征提取

包含：
1. 文本特征提取器（BERT/RoBERTa）
2. URL特征提取器
3. 元数据特征提取器
"""

import torch
import torch.nn as nn
from transformers import AutoTokenizer, AutoModel
import re
import hashlib
import tldextract
from urllib.parse import urlparse
import numpy as np
from typing import List, Dict, Any, Optional
import logging


class TextFeatureExtractor:
    """
    文本特征提取器
    使用BERT/RoBERTa提取文本特征
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.model_name = config.get('model_name', 'bert-base-uncased')
        self.max_length = config.get('max_length', 512)
        
        # 初始化模型和分词器
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModel.from_pretrained(self.model_name)
        
        # 设置为评估模式
        self.model.eval()
        
        self.logger = logging.getLogger(__name__)
    
    def extract(self, text: str) -> List[float]:
        """
        提取文本特征
        
        Args:
            text: 输入文本
            
        Returns:
            文本特征向量
        """
        try:
            # 分词
            inputs = self.tokenizer(
                text,
                max_length=self.max_length,
                padding='max_length',
                truncation=True,
                return_tensors='pt'
            )
            
            # 提取特征
            with torch.no_grad():
                outputs = self.model(**inputs)
                # 使用[CLS]标记的特征作为文本表示
                features = outputs.last_hidden_state[:, 0, :].squeeze()
            
            return features.tolist()
            
        except Exception as e:
            self.logger.error(f"Text feature extraction failed: {e}")
            # 返回零向量作为fallback
            return [0.0] * 768
    
    def extract_suspicious_patterns(self, text: str) -> Dict[str, Any]:
        """
        提取可疑文本模式
        
        Args:
            text: 输入文本
            
        Returns:
            可疑模式特征
        """
        suspicious_words = [
            'urgent', 'immediate', 'verify', 'suspend', 'click here',
            'act now', 'limited time', 'expire', 'confirm', 'update',
            'security alert', 'account locked', 'unauthorized access'
        ]
        
        # 转换为小写
        text_lower = text.lower()
        
        # 统计可疑词汇
        suspicious_count = sum(1 for word in suspicious_words if word in text_lower)
        
        # 检测URL模式
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        urls = re.findall(url_pattern, text)
        
        # 检测邮箱模式
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        
        return {
            'suspicious_word_count': suspicious_count,
            'suspicious_word_ratio': suspicious_count / len(text.split()) if text.split() else 0,
            'url_count': len(urls),
            'email_count': len(emails),
            'has_urgent_language': any(word in text_lower for word in ['urgent', 'immediate', 'act now']),
            'has_verification_request': any(word in text_lower for word in ['verify', 'confirm', 'update']),
            'text_length': len(text),
            'word_count': len(text.split())
        }


class URLFeatureExtractor:
    """
    URL特征提取器
    分析URL的各种特征
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def extract_urls(self, text: str) -> List[str]:
        """
        从文本中提取URL
        
        Args:
            text: 输入文本
            
        Returns:
            URL列表
        """
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        urls = re.findall(url_pattern, text)
        return urls
    
    def extract(self, urls: List[str]) -> List[List[float]]:
        """
        提取URL特征
        
        Args:
            urls: URL列表
            
        Returns:
            URL特征列表
        """
        if not urls:
            return []
        
        url_features = []
        for url in urls:
            features = self._extract_single_url_features(url)
            url_features.append(features)
        
        return url_features
    
    def _extract_single_url_features(self, url: str) -> List[float]:
        """
        提取单个URL的特征
        
        Args:
            url: URL字符串
            
        Returns:
            URL特征向量
        """
        try:
            parsed = urlparse(url)
            extracted = tldextract.extract(url)
            
            features = []
            
            # 基础特征
            features.append(len(url))  # URL长度
            features.append(len(parsed.path))  # 路径长度
            features.append(len(parsed.query))  # 查询参数长度
            features.append(1.0 if parsed.scheme == 'https' else 0.0)  # 是否HTTPS
            
            # 域名特征
            features.append(len(extracted.domain))  # 域名长度
            features.append(len(extracted.subdomain))  # 子域名长度
            features.append(1.0 if extracted.subdomain else 0.0)  # 是否有子域名
            
            # 可疑特征
            features.append(1.0 if self._is_ip_address(parsed.netloc) else 0.0)  # 是否IP地址
            features.append(1.0 if self._has_suspicious_tld(extracted.suffix) else 0.0)  # 可疑顶级域名
            features.append(url.count('-'))  # 连字符数量
            features.append(url.count('.'))  # 点号数量
            features.append(url.count('/'))  # 斜杠数量
            
            # 编码特征
            features.append(1.0 if '%' in url else 0.0)  # 是否有URL编码
            features.append(url.count('%'))  # URL编码数量
            
            # 填充到固定长度
            while len(features) < 512:
                features.append(0.0)
            
            return features[:512]
            
        except Exception as e:
            self.logger.error(f"URL feature extraction failed for {url}: {e}")
            return [0.0] * 512
    
    def _is_ip_address(self, netloc: str) -> bool:
        """检查是否为IP地址"""
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(ip_pattern, netloc.split(':')[0]))
    
    def _has_suspicious_tld(self, tld: str) -> bool:
        """检查是否为可疑顶级域名"""
        suspicious_tlds = ['.tk', '.ml', '.ga', '.cf', '.click', '.download']
        return any(tld.endswith(suspicious) for suspicious in suspicious_tlds)


class MetadataFeatureExtractor:
    """
    元数据特征提取器
    提取邮件元数据特征
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def extract(self, metadata: Dict[str, Any]) -> Dict[str, List[float]]:
        """
        提取元数据特征
        
        Args:
            metadata: 邮件元数据字典
            
        Returns:
            元数据特征字典
        """
        features = {}
        
        # 发件人特征
        sender = metadata.get('sender', '')
        features['sender'] = self._extract_sender_features(sender)
        
        # 时间特征
        timestamp = metadata.get('timestamp', '')
        features['time'] = self._extract_time_features(timestamp)
        
        # 邮件头特征
        headers = metadata.get('headers', {})
        features['headers'] = self._extract_header_features(headers)
        
        # 附件特征
        attachments = metadata.get('attachments', [])
        features['attachments'] = self._extract_attachment_features(attachments)
        
        return features
    
    def _extract_sender_features(self, sender: str) -> List[float]:
        """
        提取发件人特征
        
        Args:
            sender: 发件人邮箱
            
        Returns:
            发件人特征向量
        """
        features = []
        
        if not sender:
            return [0.0] * 64
        
        try:
            # 邮箱格式验证
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            features.append(1.0 if re.match(email_pattern, sender) else 0.0)
            
            # 域名特征
            if '@' in sender:
                domain = sender.split('@')[1]
                features.append(len(domain))  # 域名长度
                features.append(domain.count('.'))  # 点号数量
                features.append(1.0 if self._is_suspicious_domain(domain) else 0.0)  # 可疑域名
            else:
                features.extend([0.0, 0.0, 0.0])
            
            # 用户名特征
            if '@' in sender:
                username = sender.split('@')[0]
                features.append(len(username))  # 用户名长度
                features.append(username.count('.'))  # 点号数量
                features.append(username.count('_'))  # 下划线数量
                features.append(1.0 if username.isdigit() else 0.0)  # 是否全数字
            else:
                features.extend([0.0, 0.0, 0.0, 0.0])
            
            # 填充到固定长度
            while len(features) < 64:
                features.append(0.0)
            
            return features[:64]
            
        except Exception as e:
            self.logger.error(f"Sender feature extraction failed: {e}")
            return [0.0] * 64
    
    def _extract_time_features(self, timestamp: str) -> List[float]:
        """
        提取时间特征
        
        Args:
            timestamp: 时间戳
            
        Returns:
            时间特征向量
        """
        features = []
        
        try:
            # 这里可以添加时间相关的特征提取
            # 例如：是否在工作时间、是否周末等
            features.extend([0.0] * 32)  # 占位符
            
            return features
            
        except Exception as e:
            self.logger.error(f"Time feature extraction failed: {e}")
            return [0.0] * 32
    
    def _extract_header_features(self, headers: Dict) -> List[float]:
        """
        提取邮件头特征
        
        Args:
            headers: 邮件头字典
            
        Returns:
            邮件头特征向量
        """
        features = []
        
        try:
            # SPF检查
            spf_result = headers.get('spf', '')
            features.append(1.0 if 'pass' in spf_result.lower() else 0.0)
            
            # DKIM检查
            dkim_result = headers.get('dkim', '')
            features.append(1.0 if 'pass' in dkim_result.lower() else 0.0)
            
            # DMARC检查
            dmarc_result = headers.get('dmarc', '')
            features.append(1.0 if 'pass' in dmarc_result.lower() else 0.0)
            
            # 填充到固定长度
            while len(features) < 96:
                features.append(0.0)
            
            return features[:96]
            
        except Exception as e:
            self.logger.error(f"Header feature extraction failed: {e}")
            return [0.0] * 96
    
    def _extract_attachment_features(self, attachments: List[Dict]) -> List[float]:
        """
        提取附件特征
        
        Args:
            attachments: 附件列表
            
        Returns:
            附件特征向量
        """
        features = []
        
        try:
            # 附件数量
            features.append(len(attachments))
            
            # 可疑附件类型
            suspicious_extensions = ['.exe', '.scr', '.bat', '.com', '.pif', '.vbs', '.js']
            suspicious_count = 0
            
            for attachment in attachments:
                filename = attachment.get('filename', '')
                if any(filename.lower().endswith(ext) for ext in suspicious_extensions):
                    suspicious_count += 1
            
            features.append(suspicious_count)
            features.append(1.0 if suspicious_count > 0 else 0.0)
            
            # 填充到固定长度
            while len(features) < 64:
                features.append(0.0)
            
            return features[:64]
            
        except Exception as e:
            self.logger.error(f"Attachment feature extraction failed: {e}")
            return [0.0] * 64
    
    def _is_suspicious_domain(self, domain: str) -> bool:
        """检查是否为可疑域名"""
        suspicious_patterns = [
            'secure', 'verify', 'update', 'confirm', 'account',
            'bank', 'paypal', 'amazon', 'microsoft', 'apple'
        ]
        
        domain_lower = domain.lower()
        return any(pattern in domain_lower for pattern in suspicious_patterns)
