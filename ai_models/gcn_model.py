"""
图卷积网络模型
实现多模态特征融合的GCN模型

创新点一的核心组件：
1. 异构图构建
2. 图注意力机制
3. 动态特征融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, global_mean_pool
from torch_geometric.data import Data, Batch
import networkx as nx
from typing import Dict, List, Tuple, Optional
import numpy as np


class MultiModalGCN(nn.Module):
    """
    多模态图卷积网络
    支持异构图和多模态特征融合
    """
    
    def __init__(self, config: Dict):
        super(MultiModalGCN, self).__init__()
        
        self.config = config
        self.input_dim = config.get('input_dim', 768)
        self.hidden_dim = config.get('hidden_dim', 256)
        self.output_dim = config.get('output_dim', 2)
        self.num_layers = config.get('num_layers', 3)
        self.dropout = config.get('dropout', 0.3)
        
        # 特征映射层
        self.text_projection = nn.Linear(768, self.hidden_dim)
        self.url_projection = nn.Linear(512, self.hidden_dim)
        self.metadata_projection = nn.Linear(256, self.hidden_dim)
        
        # 图卷积层
        self.gcn_layers = nn.ModuleList()
        for i in range(self.num_layers):
            if i == 0:
                self.gcn_layers.append(GCNConv(self.hidden_dim, self.hidden_dim))
            else:
                self.gcn_layers.append(GCNConv(self.hidden_dim, self.hidden_dim))
        
        # 图注意力层
        self.gat_layers = nn.ModuleList()
        for i in range(self.num_layers):
            self.gat_layers.append(GATConv(self.hidden_dim, self.hidden_dim, heads=4, concat=False))
        
        # 门控融合机制
        self.gate_network = nn.Sequential(
            nn.Linear(self.hidden_dim * 2, self.hidden_dim),
            nn.Sigmoid()
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim // 2, self.output_dim)
        )
    
    def forward(self, data):
        """
        前向传播
        
        Args:
            data: 图数据或特征张量
            
        Returns:
            分类结果
        """
        if isinstance(data, torch.Tensor):
            # 简化输入：直接使用特征张量
            x = data
            return self.classifier(x)
        
        # 完整的图处理
        x, edge_index, batch = data.x, data.edge_index, data.batch
        
        # 特征投影
        x = self._project_features(x, data.node_types)
        
        # 图卷积
        gcn_out = self._apply_gcn_layers(x, edge_index)
        
        # 图注意力
        gat_out = self._apply_gat_layers(x, edge_index)
        
        # 门控融合
        fused_features = self._gate_fusion(gcn_out, gat_out)
        
        # 图级别池化
        graph_features = global_mean_pool(fused_features, batch)
        
        # 分类
        output = self.classifier(graph_features)
        
        return output
    
    def forward_with_params(self, x, params):
        """
        使用指定参数进行前向传播（用于MAML）
        """
        # 简化实现：直接使用分类器
        return self.classifier(x)
    
    def _project_features(self, x, node_types):
        """
        根据节点类型投影特征
        """
        projected_features = []
        
        for i, node_type in enumerate(node_types):
            if node_type == 'text':
                projected = self.text_projection(x[i])
            elif node_type == 'url':
                projected = self.url_projection(x[i])
            elif node_type == 'metadata':
                projected = self.metadata_projection(x[i])
            else:
                projected = x[i]  # 默认不变
            
            projected_features.append(projected)
        
        return torch.stack(projected_features)
    
    def _apply_gcn_layers(self, x, edge_index):
        """
        应用GCN层
        """
        for layer in self.gcn_layers:
            x = layer(x, edge_index)
            x = F.relu(x)
            x = F.dropout(x, p=self.dropout, training=self.training)
        
        return x
    
    def _apply_gat_layers(self, x, edge_index):
        """
        应用GAT层
        """
        for layer in self.gat_layers:
            x = layer(x, edge_index)
            x = F.relu(x)
            x = F.dropout(x, p=self.dropout, training=self.training)
        
        return x
    
    def _gate_fusion(self, gcn_features, gat_features):
        """
        门控融合GCN和GAT特征
        """
        # 拼接特征
        combined = torch.cat([gcn_features, gat_features], dim=-1)
        
        # 计算门控权重
        gate = self.gate_network(combined)
        
        # 融合特征
        fused = gate * gcn_features + (1 - gate) * gat_features
        
        return fused


class HeterogeneousGraphBuilder:
    """
    异构图构建器
    将邮件数据转换为异构图结构
    """
    
    def __init__(self):
        pass
    
    def build_email_graph(self, email_features: Dict) -> Data:
        """
        构建邮件异构图
        
        Args:
            email_features: 邮件特征字典
            
        Returns:
            PyTorch Geometric Data对象
        """
        nodes = []
        node_features = []
        node_types = []
        edges = []
        
        # 邮件节点
        nodes.append('email')
        node_features.append(email_features['text'])
        node_types.append('text')
        
        node_idx = 1
        
        # URL节点
        for url_feature in email_features.get('urls', []):
            nodes.append(f'url_{node_idx}')
            node_features.append(url_feature)
            node_types.append('url')
            
            # 邮件到URL的边
            edges.append([0, node_idx])
            edges.append([node_idx, 0])  # 无向图
            
            node_idx += 1
        
        # 元数据节点
        for key, value in email_features.get('metadata', {}).items():
            nodes.append(f'meta_{key}')
            node_features.append(value)
            node_types.append('metadata')
            
            # 邮件到元数据的边
            edges.append([0, node_idx])
            edges.append([node_idx, 0])  # 无向图
            
            node_idx += 1
        
        # 转换为张量
        x = torch.stack([torch.tensor(f, dtype=torch.float32) for f in node_features])
        edge_index = torch.tensor(edges, dtype=torch.long).t().contiguous()
        
        # 创建Data对象
        data = Data(x=x, edge_index=edge_index)
        data.node_types = node_types
        data.num_nodes = len(nodes)
        
        return data
    
    def networkx_to_pyg(self, nx_graph: nx.Graph) -> Data:
        """
        将NetworkX图转换为PyTorch Geometric格式
        
        Args:
            nx_graph: NetworkX图
            
        Returns:
            PyTorch Geometric Data对象
        """
        # 节点映射
        node_mapping = {node: i for i, node in enumerate(nx_graph.nodes())}
        
        # 提取节点特征
        node_features = []
        node_types = []
        
        for node in nx_graph.nodes():
            node_data = nx_graph.nodes[node]
            features = node_data.get('features', torch.zeros(768))
            node_type = node_data.get('type', 'unknown')
            
            if isinstance(features, list):
                features = torch.tensor(features, dtype=torch.float32)
            elif not isinstance(features, torch.Tensor):
                features = torch.zeros(768)
            
            node_features.append(features)
            node_types.append(node_type)
        
        # 提取边
        edges = []
        for edge in nx_graph.edges():
            src, dst = edge
            edges.append([node_mapping[src], node_mapping[dst]])
            edges.append([node_mapping[dst], node_mapping[src]])  # 无向图
        
        # 转换为张量
        x = torch.stack(node_features)
        edge_index = torch.tensor(edges, dtype=torch.long).t().contiguous() if edges else torch.empty((2, 0), dtype=torch.long)
        
        # 创建Data对象
        data = Data(x=x, edge_index=edge_index)
        data.node_types = node_types
        data.num_nodes = len(node_features)
        
        return data
