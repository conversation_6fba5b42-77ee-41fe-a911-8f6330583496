"""
对抗增强学习模块
实现基于GAN的对抗样本生成技术

创新点二的核心组件：
1. 对抗样本生成器：基于GAN生成对抗性钓鱼邮件文本
2. 语义保留的词汇替换：如"urgent"→"immediate"
3. 句法结构扰动：被动转主动等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModel
import numpy as np
from typing import List, Dict, Tuple, Optional
import random
import re


class AdversarialGenerator(nn.Module):
    """
    基于GAN的对抗样本生成器
    生成对抗性钓鱼邮件文本，保留语义的同时增加对抗性
    """
    
    def __init__(self, vocab_size: int = 30000, embedding_dim: int = 768, hidden_dim: int = 512):
        super(AdversarialGenerator, self).__init__()
        
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        
        # 文本编码器
        self.text_encoder = nn.LSTM(embedding_dim, hidden_dim, batch_first=True, bidirectional=True)
        
        # 生成器网络
        self.generator = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, vocab_size),
            nn.Softmax(dim=-1)
        )
        
        # 判别器网络
        self.discriminator = nn.Sequential(
            nn.Linear(embedding_dim, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # 语义保留词汇替换字典
        self.semantic_substitutions = {
            'urgent': ['immediate', 'critical', 'pressing', 'vital'],
            'click': ['tap', 'select', 'press', 'activate'],
            'verify': ['confirm', 'validate', 'authenticate', 'check'],
            'account': ['profile', 'credentials', 'login', 'access'],
            'suspended': ['blocked', 'frozen', 'disabled', 'restricted'],
            'security': ['protection', 'safety', 'privacy', 'defense'],
            'update': ['refresh', 'renew', 'modify', 'revise'],
            'expire': ['end', 'terminate', 'lapse', 'conclude']
        }
        
    def forward(self, input_embeddings: torch.Tensor) -> torch.Tensor:
        """
        前向传播生成对抗样本
        
        Args:
            input_embeddings: 输入文本的嵌入表示 [batch_size, seq_len, embedding_dim]
            
        Returns:
            生成的对抗样本概率分布 [batch_size, seq_len, vocab_size]
        """
        batch_size, seq_len, _ = input_embeddings.shape
        
        # 编码输入文本
        encoded, (hidden, cell) = self.text_encoder(input_embeddings)
        
        # 生成对抗样本
        adversarial_logits = self.generator(encoded)
        
        return adversarial_logits
    
    def generate_adversarial_text(self, original_text: str, tokenizer, model, 
                                 perturbation_rate: float = 0.3) -> str:
        """
        生成对抗性文本
        
        Args:
            original_text: 原始文本
            tokenizer: 分词器
            model: 预训练模型
            perturbation_rate: 扰动比例
            
        Returns:
            对抗性文本
        """
        # 词汇替换
        adversarial_text = self._semantic_word_substitution(original_text, perturbation_rate)
        
        # 句法结构扰动
        adversarial_text = self._syntactic_perturbation(adversarial_text)
        
        return adversarial_text
    
    def _semantic_word_substitution(self, text: str, perturbation_rate: float) -> str:
        """
        语义保留的词汇替换
        
        Args:
            text: 输入文本
            perturbation_rate: 替换比例
            
        Returns:
            替换后的文本
        """
        words = text.lower().split()
        num_substitutions = int(len(words) * perturbation_rate)
        
        # 随机选择要替换的词汇
        substitution_indices = random.sample(range(len(words)), 
                                           min(num_substitutions, len(words)))
        
        for idx in substitution_indices:
            word = words[idx]
            if word in self.semantic_substitutions:
                # 随机选择同义词替换
                synonyms = self.semantic_substitutions[word]
                words[idx] = random.choice(synonyms)
        
        return ' '.join(words)
    
    def _syntactic_perturbation(self, text: str) -> str:
        """
        句法结构扰动
        
        Args:
            text: 输入文本
            
        Returns:
            扰动后的文本
        """
        # 被动语态转主动语态
        text = self._passive_to_active(text)
        
        # 句子重排
        text = self._sentence_reordering(text)
        
        return text
    
    def _passive_to_active(self, text: str) -> str:
        """
        被动语态转主动语态
        """
        # 简单的被动转主动规则
        passive_patterns = [
            (r'(\w+) is (\w+ed) by (\w+)', r'\3 \2 \1'),
            (r'(\w+) was (\w+ed) by (\w+)', r'\3 \2 \1'),
            (r'(\w+) has been (\w+ed) by (\w+)', r'\3 has \2 \1')
        ]
        
        for pattern, replacement in passive_patterns:
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        return text
    
    def _sentence_reordering(self, text: str) -> str:
        """
        句子重排
        """
        sentences = text.split('.')
        if len(sentences) > 2:
            # 随机重排句子顺序
            random.shuffle(sentences)
        
        return '.'.join(sentences)


class AdversarialTrainer:
    """
    对抗训练器
    管理GAN的训练过程
    """
    
    def __init__(self, generator: AdversarialGenerator, device: str = 'cuda'):
        self.generator = generator
        self.device = device
        
        # 优化器
        self.gen_optimizer = torch.optim.Adam(generator.generator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        self.disc_optimizer = torch.optim.Adam(generator.discriminator.parameters(), lr=0.0002, betas=(0.5, 0.999))
        
        # 损失函数
        self.adversarial_loss = nn.BCELoss()
        self.semantic_loss = nn.MSELoss()
        
    def train_step(self, real_embeddings: torch.Tensor, fake_embeddings: torch.Tensor) -> Dict[str, float]:
        """
        执行一步对抗训练
        
        Args:
            real_embeddings: 真实文本嵌入
            fake_embeddings: 生成的对抗文本嵌入
            
        Returns:
            训练损失字典
        """
        batch_size = real_embeddings.size(0)
        
        # 真实和虚假标签
        real_labels = torch.ones(batch_size, 1).to(self.device)
        fake_labels = torch.zeros(batch_size, 1).to(self.device)
        
        # 训练判别器
        self.disc_optimizer.zero_grad()
        
        # 真实样本
        real_output = self.generator.discriminator(real_embeddings)
        real_loss = self.adversarial_loss(real_output, real_labels)
        
        # 虚假样本
        fake_output = self.generator.discriminator(fake_embeddings.detach())
        fake_loss = self.adversarial_loss(fake_output, fake_labels)
        
        disc_loss = (real_loss + fake_loss) / 2
        disc_loss.backward()
        self.disc_optimizer.step()
        
        # 训练生成器
        self.gen_optimizer.zero_grad()
        
        # 生成器希望判别器认为生成的样本是真实的
        fake_output = self.generator.discriminator(fake_embeddings)
        gen_adversarial_loss = self.adversarial_loss(fake_output, real_labels)
        
        # 语义保留损失
        semantic_loss = self.semantic_loss(fake_embeddings, real_embeddings)
        
        # 总生成器损失
        gen_loss = gen_adversarial_loss + 0.1 * semantic_loss
        gen_loss.backward()
        self.gen_optimizer.step()
        
        return {
            'discriminator_loss': disc_loss.item(),
            'generator_loss': gen_loss.item(),
            'semantic_loss': semantic_loss.item()
        }
    
    def generate_adversarial_samples(self, texts: List[str], tokenizer, model, 
                                   num_samples: int = 5) -> List[str]:
        """
        批量生成对抗样本
        
        Args:
            texts: 原始文本列表
            tokenizer: 分词器
            model: 预训练模型
            num_samples: 每个文本生成的对抗样本数量
            
        Returns:
            对抗样本列表
        """
        adversarial_samples = []
        
        for text in texts:
            for _ in range(num_samples):
                adversarial_text = self.generator.generate_adversarial_text(
                    text, tokenizer, model
                )
                adversarial_samples.append(adversarial_text)
        
        return adversarial_samples
