#!/usr/bin/env python3
"""
测试AI集成模块
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.phishing_detector.settings')
sys.path.append('backend')
django.setup()

from backend.detection.ai_integration import AIModelManager

def test_ai_integration():
    """测试AI集成功能"""
    print("🧪 测试AI集成模块...")
    
    try:
        # 创建AI模型管理器
        ai_manager = AIModelManager()
        print("✅ AI模型管理器创建成功")
        
        # 测试邮件内容
        test_email = """
Subject: 紧急安全警报
From: <EMAIL>
To: <EMAIL>

尊敬的客户：

我们检测到您的账户存在异常登录。请立即点击以下链接验证您的身份：

http://fake-verify.com/login

如果您不在24小时内完成验证，您的账户将被暂停。

银行安全中心
        """
        
        # 测试检测功能
        print("🔍 测试邮件检测...")
        result = ai_manager.detect_email(test_email, {})
        
        print("📊 检测结果:")
        print(f"   是否钓鱼邮件: {result['is_phishing']}")
        print(f"   置信度: {result['confidence']:.3f}")
        print(f"   风险等级: {result['risk_level']}")
        
        # 测试URL提取
        print("🔗 测试URL提取...")
        urls = ai_manager._extract_urls(test_email)
        print(f"   提取到的URL: {urls}")
        
        print("✅ 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_integration()
