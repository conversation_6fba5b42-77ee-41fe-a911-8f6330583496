"""
测试批量检测历史记录修复
验证批量检测结果是否正确写入检测历史
"""

import requests
import time
import json
import os
import zipfile
from pathlib import Path

class BatchHistoryTester:
    def __init__(self):
        self.api_base = "http://localhost:8000/api"
        
    def create_test_emails(self, count=5):
        """创建测试邮件文件"""
        test_dir = Path("test_batch_history")
        test_dir.mkdir(exist_ok=True)
        
        # 创建测试邮件
        emails = []
        for i in range(count):
            filename = f"test_email_{i+1}.txt"
            filepath = test_dir / filename
            
            if i % 2 == 0:
                # 正常邮件
                content = f"""Subject: Normal Email {i+1}
From: user{i+1}@company.com
To: <EMAIL>

This is a normal business email #{i+1}.
Please review the attached document.

Best regards,
User {i+1}
"""
            else:
                # 钓鱼邮件
                content = f"""Subject: Urgent: Account Verification Required {i+1}
From: security@fake-bank{i+1}.com
To: <EMAIL>

Your account will be suspended unless you verify immediately!
Click here: http://fake-bank{i+1}.com/verify
Provide your password and credit card details.

Bank Security Team
"""
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            emails.append({
                'filename': filename,
                'path': str(filepath)
            })
        
        # 创建ZIP文件
        zip_path = test_dir / "test_emails.zip"
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for email in emails:
                zipf.write(email['path'], email['filename'])
        
        return str(zip_path), emails
    
    def get_history_count_before(self):
        """获取批量检测前的历史记录数量"""
        try:
            response = requests.get(f"{self.api_base}/history/")
            if response.status_code == 200:
                data = response.json()
                return data.get('total_count', 0)
            return 0
        except Exception as e:
            print(f"获取历史记录失败: {e}")
            return 0
    
    def upload_batch(self, zip_path):
        """上传批量文件"""
        try:
            with open(zip_path, 'rb') as f:
                files = {'zip_file': f}
                response = requests.post(f"{self.api_base}/batch/upload/", files=files)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 批量上传成功: {data['message']}")
                return data['batch_id']
            else:
                print(f"❌ 批量上传失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 批量上传异常: {e}")
            return None
    
    def start_detection(self, batch_id, mode="standard"):
        """开始批量检测"""
        try:
            data = {
                'batch_id': batch_id,
                'detection_mode': mode
            }
            response = requests.post(f"{self.api_base}/batch/detect/", json=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 检测启动成功: {result['message']}")
                return True
            else:
                print(f"❌ 检测启动失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 检测启动异常: {e}")
            return False
    
    def wait_for_completion(self, batch_id, max_wait=60):
        """等待检测完成"""
        print("⏳ 等待检测完成...")
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                response = requests.get(f"{self.api_base}/batch/status/{batch_id}/")
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data['status']
                    progress = status_data.get('progress', 0)
                    
                    print(f"   状态: {status}, 进度: {progress:.1f}%")
                    
                    if status == 'completed':
                        print("✅ 检测完成!")
                        return True
                    elif status == 'failed':
                        print("❌ 检测失败!")
                        return False
                
                time.sleep(2)
                
            except Exception as e:
                print(f"获取状态失败: {e}")
                time.sleep(2)
        
        print("⏰ 等待超时")
        return False
    
    def get_history_count_after(self):
        """获取批量检测后的历史记录数量"""
        return self.get_history_count_before()
    
    def verify_history_records(self, expected_count):
        """验证历史记录"""
        try:
            response = requests.get(f"{self.api_base}/history/?page_size=100")
            if response.status_code == 200:
                data = response.json()
                records = data.get('results', [])
                
                print(f"\n📊 历史记录验证:")
                print(f"   总记录数: {len(records)}")
                
                # 显示最近的几条记录
                recent_records = records[:5]
                for record in recent_records:
                    print(f"   - {record['filename']}: {'钓鱼' if record['is_phishing'] else '正常'} "
                          f"(置信度: {record['confidence']:.3f})")
                
                return len(records)
            
        except Exception as e:
            print(f"验证历史记录失败: {e}")
            return 0
    
    def cleanup(self):
        """清理测试文件"""
        import shutil
        test_dir = Path("test_batch_history")
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print("🧹 清理测试文件完成")
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始批量检测历史记录测试\n")
        
        try:
            # 1. 创建测试邮件
            print("1️⃣ 创建测试邮件...")
            zip_path, emails = self.create_test_emails(5)
            print(f"   创建了 {len(emails)} 个测试邮件")
            
            # 2. 获取检测前的历史记录数量
            print("\n2️⃣ 获取检测前历史记录数量...")
            count_before = self.get_history_count_before()
            print(f"   检测前历史记录数: {count_before}")
            
            # 3. 上传批量文件
            print("\n3️⃣ 上传批量文件...")
            batch_id = self.upload_batch(zip_path)
            if not batch_id:
                return False
            
            # 4. 开始检测
            print("\n4️⃣ 开始批量检测...")
            if not self.start_detection(batch_id):
                return False
            
            # 5. 等待完成
            print("\n5️⃣ 等待检测完成...")
            if not self.wait_for_completion(batch_id):
                return False
            
            # 6. 获取检测后的历史记录数量
            print("\n6️⃣ 获取检测后历史记录数量...")
            time.sleep(2)  # 等待数据库写入
            count_after = self.get_history_count_after()
            print(f"   检测后历史记录数: {count_after}")
            
            # 7. 验证结果
            print("\n7️⃣ 验证结果...")
            expected_increase = len(emails)
            actual_increase = count_after - count_before
            
            print(f"   预期增加: {expected_increase} 条记录")
            print(f"   实际增加: {actual_increase} 条记录")
            
            if actual_increase == expected_increase:
                print("✅ 测试通过! 批量检测正确写入了历史记录")
                self.verify_history_records(count_after)
                return True
            else:
                print("❌ 测试失败! 历史记录数量不匹配")
                return False
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return False
        
        finally:
            # 清理测试文件
            self.cleanup()


if __name__ == "__main__":
    tester = BatchHistoryTester()
    success = tester.run_test()
    
    if success:
        print("\n🎉 批量检测历史记录功能修复成功!")
    else:
        print("\n💥 批量检测历史记录功能仍有问题，需要进一步调试")
