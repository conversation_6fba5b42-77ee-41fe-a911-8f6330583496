#!/usr/bin/env python3
"""
MySQL数据库设置脚本
自动创建数据库和配置用户权限

使用方法：
python setup_mysql.py --create-db
python setup_mysql.py --test-connection
"""

import mysql.connector
import argparse
import sys
from mysql.connector import Error

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'root',  # 请根据您的MySQL root密码修改
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

DATABASE_NAME = 'phishing_detector'

def create_database():
    """创建数据库"""
    try:
        print("🔗 连接到MySQL服务器...")
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # 创建数据库
            print(f"📊 创建数据库: {DATABASE_NAME}")
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DATABASE_NAME} "
                         f"CHARACTER SET {DB_CONFIG['charset']} "
                         f"COLLATE {DB_CONFIG['collation']}")
            
            # 显示数据库
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            
            print("✅ 数据库创建成功！")
            print("📋 当前数据库列表:")
            for db in databases:
                marker = " ✓" if db[0] == DATABASE_NAME else ""
                print(f"   - {db[0]}{marker}")
            
            cursor.close()
            connection.close()
            
            return True
            
    except Error as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

def test_connection():
    """测试数据库连接"""
    try:
        print("🔗 测试数据库连接...")
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DATABASE_NAME,
            charset=DB_CONFIG['charset']
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # 获取数据库信息
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()
            
            cursor.execute("SHOW VARIABLES LIKE 'character_set_database'")
            charset_info = cursor.fetchone()
            
            print("✅ 数据库连接成功！")
            print(f"📊 MySQL版本: {version[0]}")
            print(f"📁 当前数据库: {current_db[0]}")
            print(f"🔤 字符集: {charset_info[1]}")
            
            # 测试创建表权限
            test_table_sql = """
            CREATE TABLE IF NOT EXISTS connection_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                test_field VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """
            
            cursor.execute(test_table_sql)
            cursor.execute("INSERT INTO connection_test (test_field) VALUES ('测试数据')")
            cursor.execute("SELECT * FROM connection_test")
            test_data = cursor.fetchall()
            cursor.execute("DROP TABLE connection_test")
            
            print(f"🧪 表操作测试: 成功 (插入了 {len(test_data)} 条记录)")
            
            cursor.close()
            connection.close()
            
            return True
            
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 检查MySQL服务是否启动")
        print("2. 确认用户名和密码是否正确")
        print("3. 确认数据库是否已创建")
        print("4. 检查防火墙设置")
        return False

def show_django_config():
    """显示Django配置信息"""
    print("\n" + "="*60)
    print("📋 Django数据库配置")
    print("="*60)
    print("请确保 backend/phishing_detector/settings.py 中的数据库配置如下：")
    print()
    print("DATABASES = {")
    print("    'default': {")
    print("        'ENGINE': 'django.db.backends.mysql',")
    print(f"        'NAME': '{DATABASE_NAME}',")
    print(f"        'USER': '{DB_CONFIG['user']}',")
    print(f"        'PASSWORD': '{DB_CONFIG['password']}',")
    print(f"        'HOST': '{DB_CONFIG['host']}',")
    print(f"        'PORT': '{DB_CONFIG['port']}',")
    print("        'OPTIONS': {")
    print(f"            'charset': '{DB_CONFIG['charset']}',")
    print("            'use_unicode': True,")
    print("            'init_command': \"SET sql_mode='STRICT_TRANS_TABLES'\",")
    print("            'autocommit': True,")
    print("        },")
    print("        'CONN_MAX_AGE': 60,")
    print("        'CONN_HEALTH_CHECKS': True,")
    print("    }")
    print("}")
    print()
    print("🚀 下一步操作:")
    print("1. cd backend")
    print("2. python manage.py makemigrations")
    print("3. python manage.py migrate")
    print("4. python manage.py runserver")
    print("="*60)

def main():
    parser = argparse.ArgumentParser(description='MySQL数据库设置工具')
    parser.add_argument('--create-db', action='store_true', help='创建数据库')
    parser.add_argument('--test-connection', action='store_true', help='测试数据库连接')
    parser.add_argument('--show-config', action='store_true', help='显示Django配置')
    
    args = parser.parse_args()
    
    if args.create_db:
        success = create_database()
        if success:
            show_django_config()
    elif args.test_connection:
        test_connection()
    elif args.show_config:
        show_django_config()
    else:
        print("🚀 MySQL数据库设置工具")
        print("=" * 40)
        print("使用方法:")
        print("  python setup_mysql.py --create-db      # 创建数据库")
        print("  python setup_mysql.py --test-connection # 测试连接")
        print("  python setup_mysql.py --show-config     # 显示配置")
        print()
        print("💡 建议执行顺序:")
        print("1. python setup_mysql.py --create-db")
        print("2. python setup_mysql.py --test-connection")
        print("3. cd backend && python manage.py migrate")

if __name__ == "__main__":
    main()
