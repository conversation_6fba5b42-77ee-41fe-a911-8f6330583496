"""
批量测试邮件生成器
快速生成大量测试邮件用于批量检测功能验证

使用方法：
python generate_test_emails.py --count 1000 --output test_emails.zip
"""

import os
import random
import zipfile
import argparse
from datetime import datetime, timedelta


class EmailGenerator:
    """邮件生成器"""
    
    def __init__(self):
        # 扩展的正常邮件模板（增加更多样性）
        self.normal_templates = [
            """Subject: 会议通知 - {meeting_topic}
From: {sender}@{company_domain}
To: team@{company_domain}
Date: {date}

各位同事：

定于{meeting_date}在{location}召开{meeting_topic}会议。

会议议程：
- {agenda_1}
- {agenda_2}
- {agenda_3}

请准时参加。

{sender_name}
{department}""",

            """Subject: Weekly Report - {week}
From: {sender}@{company_domain}
To: manager@{company_domain}
Date: {date}

Dear Manager,

Here is my weekly report for {week}:

Completed Tasks:
- {task_1}
- {task_2}
- {task_3}

Next Week Plans:
- {plan_1}
- {plan_2}

Best regards,
{sender_name}""",

            """Subject: System Maintenance Notice
From: it-support@{company_domain}
To: all-users@{company_domain}
Date: {date}

Dear Users,

We will perform system maintenance on {maintenance_date}.

Maintenance Details:
- Time: {maintenance_time}
- Duration: {duration} hours
- Affected Services: {services}

Thank you for your patience.

IT Support Team""",
        ]
        
        # 钓鱼邮件模板
        self.phishing_templates = [
            """Subject: 紧急：您的{bank_name}账户安全警报
From: security@{fake_domain}
To: <EMAIL>
Date: {date}

尊敬的客户：

我们检测到您的账户存在异常活动。

账户信息：
- 账户：****{account_suffix}
- 异常时间：{alert_time}
- 风险等级：{risk_level}

请立即验证：{phishing_url}

必须在{deadline}小时内完成验证。

{bank_name}安全部门""",

            """Subject: Your {service} Account - Action Required
From: security@{fake_domain}
To: <EMAIL>
Date: {date}

Dear User,

Your {service} account has been temporarily {action}.

Reason: {reason}
Account: {account_id}
Status: {status}

Verify your account: {phishing_url}

Required information:
- Account credentials
- Phone verification
- Security questions

{service} Security Team""",

            """Subject: Payment Failed - Order #{order_id}
From: orders@{fake_domain}
To: <EMAIL>
Date: {date}

Dear Customer,

Your recent order could not be processed.

Order Details:
- Order #: {order_id}
- Amount: ${amount}
- Status: Payment Failed

Update payment method: {phishing_url}

Your order will be cancelled in {cancel_hours} hours.

Customer Service""",
        ]
        
        # 零样本测试邮件模板（新兴攻击类型）
        self.zero_shot_templates = [
            """Subject: AI Assistant Subscription Renewal
From: ai-support@{fake_domain}
To: <EMAIL>
Date: {date}

Hello AI User,

Your {ai_service} subscription expires in {expire_days} days.

Current Plan: {plan_type}
Usage: {usage_percent}% of monthly limit
Renewal Required: {renewal_date}

Renew subscription: {phishing_url}

Features at risk:
• Smart recommendations
• Automated responses
• Premium AI models

AI Support Team""",

            """Subject: NFT Collection Verification Required
From: nft-verify@{fake_domain}
To: <EMAIL>
Date: {date}

Dear NFT Collector,

New blockchain regulations require verification.

Collection Stats:
- Value: ${nft_value}
- Items: {nft_count} NFTs
- Blockchain: {blockchain}

Verify ownership: {phishing_url}

Deadline: {deadline} hours

NFT Compliance Team""",

            """Subject: Metaverse Account Security Update
From: metaverse-security@{fake_domain}
To: <EMAIL>
Date: {date}

Virtual World User,

Your metaverse account needs security update.

Avatar: {avatar_name}
Virtual Assets: ${virtual_assets}
World: {virtual_world}

Update security: {phishing_url}

Failure to update may result in asset loss.

Metaverse Security""",
        ]
        
        # 数据变量
        self.companies = ['techcorp', 'innovate', 'solutions', 'systems', 'digital']
        self.domains = ['company.com', 'corp.net', 'business.org']
        self.fake_domains = ['secure-verify.com', 'account-check.net', 'user-confirm.org']
        self.banks = ['工商银行', '建设银行', '农业银行', '中国银行']
        self.services = ['PayPal', 'Amazon', 'Microsoft', 'Google']
        self.meeting_topics = ['项目评审', '季度总结', '培训会议', '团建活动']
        self.locations = ['会议室A', '会议室B', '多功能厅', '培训室']
        
    def generate_normal_email(self) -> str:
        """生成正常邮件"""
        template = random.choice(self.normal_templates)
        
        # 生成随机日期
        days_ago = random.randint(0, 30)
        date = (datetime.now() - timedelta(days=days_ago)).strftime("%a, %d %b %Y %H:%M:%S +0800")
        
        # 填充变量
        variables = {
            'sender': f'user{random.randint(1, 100)}',
            'sender_name': f'张{random.choice(["伟", "敏", "静", "强", "丽"])}',
            'company_domain': random.choice(self.domains),
            'department': random.choice(['技术部', '市场部', '人事部', '财务部']),
            'date': date,
            'meeting_topic': random.choice(self.meeting_topics),
            'meeting_date': f'{random.randint(1, 28)}日',
            'location': random.choice(self.locations),
            'agenda_1': '工作汇报',
            'agenda_2': '问题讨论',
            'agenda_3': '下步计划',
            'week': f'Week {random.randint(1, 52)}',
            'task_1': 'Complete project documentation',
            'task_2': 'Review code changes',
            'task_3': 'Attend team meetings',
            'plan_1': 'Start new feature development',
            'plan_2': 'Prepare presentation',
            'maintenance_date': f'{random.randint(1, 28)}日',
            'maintenance_time': f'{random.randint(20, 23)}:00-{random.randint(1, 5)}:00',
            'duration': random.randint(2, 6),
            'services': 'Email, File Server'
        }
        
        return template.format(**variables)
    
    def generate_phishing_email(self) -> str:
        """生成钓鱼邮件"""
        template = random.choice(self.phishing_templates)
        
        days_ago = random.randint(0, 7)
        date = (datetime.now() - timedelta(days=days_ago)).strftime("%a, %d %b %Y %H:%M:%S +0800")
        
        variables = {
            'bank_name': random.choice(self.banks),
            'fake_domain': random.choice(self.fake_domains),
            'date': date,
            'account_suffix': f'{random.randint(1000, 9999)}',
            'alert_time': f'{random.randint(1, 12)}:{random.randint(10, 59)}',
            'risk_level': random.choice(['高危', '中危', '严重']),
            'phishing_url': f'http://{random.choice(self.fake_domains)}/verify',
            'deadline': random.randint(12, 48),
            'service': random.choice(self.services),
            'action': random.choice(['suspended', 'limited', 'locked']),
            'reason': 'Suspicious activity detected',
            'account_id': f'ACC{random.randint(100000, 999999)}',
            'status': 'Under Review',
            'order_id': f'ORD{random.randint(100000, 999999)}',
            'amount': f'{random.randint(50, 500)}.{random.randint(10, 99)}',
            'cancel_hours': random.randint(12, 72)
        }
        
        return template.format(**variables)
    
    def generate_zero_shot_email(self) -> str:
        """生成零样本测试邮件"""
        template = random.choice(self.zero_shot_templates)
        
        days_ago = random.randint(0, 7)
        date = (datetime.now() - timedelta(days=days_ago)).strftime("%a, %d %b %Y %H:%M:%S +0800")
        
        variables = {
            'fake_domain': random.choice(self.fake_domains),
            'date': date,
            'ai_service': random.choice(['ChatGPT Plus', 'Claude Pro', 'Gemini Advanced']),
            'expire_days': random.randint(1, 7),
            'plan_type': random.choice(['Premium', 'Pro', 'Enterprise']),
            'usage_percent': random.randint(80, 99),
            'renewal_date': f'{random.randint(1, 30)}日',
            'phishing_url': f'http://{random.choice(self.fake_domains)}/renew',
            'nft_value': f'{random.randint(10000, 100000):,}',
            'nft_count': random.randint(5, 50),
            'blockchain': random.choice(['Ethereum', 'Polygon', 'Solana']),
            'deadline': random.randint(24, 72),
            'avatar_name': f'Avatar{random.randint(1000, 9999)}',
            'virtual_assets': f'{random.randint(5000, 50000):,}',
            'virtual_world': random.choice(['MetaWorld', 'VirtualSpace', 'DigitalRealm'])
        }
        
        return template.format(**variables)
    
    def generate_batch(self, total_count: int, normal_ratio: float = 0.6) -> list:
        """
        生成批量邮件
        
        Args:
            total_count: 总邮件数量
            normal_ratio: 正常邮件比例
            
        Returns:
            邮件列表，每个元素包含 (filename, content, label)
        """
        emails = []
        
        normal_count = int(total_count * normal_ratio)
        phishing_count = int(total_count * 0.3)  # 30% 传统钓鱼
        zero_shot_count = total_count - normal_count - phishing_count  # 剩余为零样本
        
        print(f"生成邮件分布：")
        print(f"  正常邮件: {normal_count}")
        print(f"  钓鱼邮件: {phishing_count}")
        print(f"  零样本邮件: {zero_shot_count}")
        
        # 生成正常邮件
        for i in range(normal_count):
            content = self.generate_normal_email()
            filename = f"normal_{i+1:06d}.txt"
            emails.append((filename, content, 0))
        
        # 生成钓鱼邮件
        for i in range(phishing_count):
            content = self.generate_phishing_email()
            filename = f"phishing_{i+1:06d}.txt"
            emails.append((filename, content, 1))
        
        # 生成零样本邮件
        for i in range(zero_shot_count):
            content = self.generate_zero_shot_email()
            filename = f"zero_shot_{i+1:06d}.txt"
            emails.append((filename, content, 1))
        
        # 随机打乱
        random.shuffle(emails)
        
        return emails


def main():
    parser = argparse.ArgumentParser(description='生成批量测试邮件')
    parser.add_argument('--count', type=int, default=1000, help='生成邮件数量')
    parser.add_argument('--output', type=str, default='test_emails.zip', help='输出ZIP文件名')
    parser.add_argument('--normal-ratio', type=float, default=0.6, help='正常邮件比例')
    
    args = parser.parse_args()
    
    print(f"🚀 开始生成 {args.count} 个测试邮件...")
    
    # 创建生成器
    generator = EmailGenerator()
    
    # 生成邮件
    emails = generator.generate_batch(args.count, args.normal_ratio)
    
    # 创建ZIP文件
    print(f"📦 创建ZIP文件: {args.output}")
    with zipfile.ZipFile(args.output, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for filename, content, label in emails:
            zipf.writestr(filename, content)
    
    # 生成标签文件
    label_file = args.output.replace('.zip', '_labels.txt')
    print(f"📝 创建标签文件: {label_file}")
    with open(label_file, 'w', encoding='utf-8') as f:
        f.write("filename,label,type\n")
        for filename, content, label in emails:
            email_type = 'normal' if label == 0 else ('phishing' if 'phishing_' in filename else 'zero_shot')
            f.write(f"{filename},{label},{email_type}\n")
    
    print(f"✅ 完成！生成了 {len(emails)} 个邮件")
    print(f"   ZIP文件: {args.output}")
    print(f"   标签文件: {label_file}")
    print(f"\n📋 使用方法：")
    print(f"1. 启动前后端系统")
    print(f"2. 访问批量检测页面")
    print(f"3. 上传 {args.output} 文件")
    print(f"4. 选择检测模式并开始检测")


if __name__ == "__main__":
    main()
