# 批量邮件检测功能使用指南

## 功能概述

批量检测功能允许您一次性上传和检测大量邮件文件（支持上万条），非常适合：
- 大规模邮件安全审计
- 历史邮件数据分析
- 系统性能压力测试
- 研究数据集验证

## 🚀 快速开始

### 1. 生成测试数据

使用提供的测试数据生成器：

```bash
# 生成1000个测试邮件
python generate_test_emails.py --count 1000 --output test_emails_1k.zip

# 生成10000个测试邮件
python generate_test_emails.py --count 10000 --output test_emails_10k.zip

# 自定义正常邮件比例（默认60%正常，30%钓鱼，10%零样本）
python generate_test_emails.py --count 5000 --normal-ratio 0.7 --output custom_emails.zip
```

### 2. 启动系统

```bash
# 启动后端
cd backend
python manage.py runserver

# 启动前端
cd frontend
npm run dev
```

### 3. 访问批量检测

打开浏览器访问：http://localhost:5173/batch-detection

## 📋 使用流程

### 步骤1：上传邮件文件

#### 方式一：ZIP压缩包上传（推荐）
1. 选择"ZIP压缩包上传"
2. 点击上传区域或拖拽ZIP文件
3. 系统会自动解压并识别邮件文件

#### 方式二：多文件上传
1. 选择"多个文件上传"
2. 选择多个邮件文件（.txt, .eml, .msg格式）
3. 支持拖拽上传

### 步骤2：选择检测模式

- **标准检测**：检测已知类型的钓鱼邮件
- **零样本检测**：检测未知类型的钓鱼攻击（验证创新点二）

### 步骤3：开始检测

1. 点击"开始上传"按钮
2. 系统显示实时进度：
   - 总文件数
   - 已处理文件数
   - 完成百分比
   - 处理速度（文件/秒）

### 步骤4：查看结果

检测完成后可以：
- 查看检测摘要统计
- 浏览详细检测结果
- 导出CSV格式报告
- 开始新的检测任务

## 📊 结果分析

### 摘要统计
- **钓鱼邮件数量**：检测出的钓鱼邮件总数
- **正常邮件数量**：检测为正常的邮件总数
- **钓鱼率**：钓鱼邮件占总数的百分比
- **平均置信度**：所有检测结果的平均置信度

### 详细结果
每个邮件的检测结果包括：
- 文件名
- 检测结果（钓鱼邮件/正常邮件）
- 置信度百分比
- 风险等级（安全/低风险/中风险/高风险）

### 导出功能
- 支持导出CSV格式
- 包含所有检测详情
- 便于进一步分析和报告

## 🎯 性能测试建议

### 小规模测试（100-1000邮件）
```bash
python generate_test_emails.py --count 500 --output small_test.zip
```
- 用于功能验证
- 快速查看结果
- 调试系统问题

### 中规模测试（1000-5000邮件）
```bash
python generate_test_emails.py --count 3000 --output medium_test.zip
```
- 性能基准测试
- 验证并发处理能力
- 评估检测准确率

### 大规模测试（5000-10000+邮件）
```bash
python generate_test_emails.py --count 10000 --output large_test.zip
```
- 压力测试
- 验证系统稳定性
- 评估处理速度

## 🔧 技术特点

### 后端特性
- **并发处理**：使用线程池并行检测（最多10个并发）
- **实时状态**：每2秒更新检测进度
- **错误处理**：单个文件失败不影响整体进程
- **内存优化**：流式处理大文件，避免内存溢出

### 前端特性
- **拖拽上传**：支持文件拖拽到上传区域
- **实时进度**：动态显示检测进度和速度
- **分页显示**：大量结果分页展示，提升性能
- **响应式设计**：适配不同屏幕尺寸

### AI模型集成
- **智能检测**：集成零样本学习模型
- **动态阈值**：根据邮件特征调整检测阈值
- **多模态分析**：综合文本、URL、元数据特征
- **回退机制**：AI模型不可用时使用启发式检测

## 📈 预期性能指标

### 处理速度
- **小文件**（<1KB）：~10-20 文件/秒
- **中等文件**（1-10KB）：~5-10 文件/秒
- **大文件**（>10KB）：~2-5 文件/秒

### 准确率目标
- **正常邮件识别**：>95%
- **传统钓鱼邮件**：>90%
- **零样本钓鱼邮件**：>80%

### 系统资源
- **内存使用**：<2GB（10000邮件）
- **CPU使用**：中等负载
- **磁盘空间**：临时文件自动清理

## 🛠️ 故障排除

### 常见问题

#### 1. 上传失败
- 检查文件格式（支持.txt, .eml, .msg）
- 确认ZIP文件未损坏
- 检查文件大小限制

#### 2. 检测速度慢
- 减少并发线程数
- 检查系统资源使用情况
- 确认网络连接稳定

#### 3. 结果不准确
- 验证AI模型是否正确加载
- 检查后端日志错误信息
- 尝试不同的检测模式

#### 4. 内存不足
- 减少批量大小
- 重启后端服务
- 检查系统可用内存

### 日志查看
```bash
# 查看后端日志
cd backend
python manage.py runserver --verbosity=2

# 查看浏览器控制台
F12 -> Console 标签
```

## 🎓 演示建议

### 学术演示
1. **展示规模**：上传1000+邮件展示系统处理能力
2. **突出创新**：使用零样本检测模式检测新兴攻击
3. **性能对比**：展示不同检测模式的效果差异
4. **实时监控**：强调实时进度和处理速度

### 技术亮点
- "支持一次性处理上万条邮件"
- "零样本学习检测未知攻击类型"
- "实时进度监控和并发处理"
- "完整的结果分析和导出功能"

## 📝 扩展功能

### 自定义数据
如需使用真实邮件数据：
1. 将邮件文件放入文件夹
2. 压缩为ZIP格式
3. 确保文件格式正确
4. 上传到批量检测系统

### 结果分析
导出的CSV文件可以用于：
- Excel数据透视表分析
- Python pandas数据分析
- 机器学习模型训练
- 安全报告生成

这个批量检测功能为您的钓鱼邮件检测系统提供了强大的大规模处理能力，特别适合验证系统的实际应用价值和技术创新点！
