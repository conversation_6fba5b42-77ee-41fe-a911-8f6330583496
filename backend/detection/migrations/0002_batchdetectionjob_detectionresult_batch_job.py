# Generated by Django 5.2.4 on 2025-07-31 07:36

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('detection', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BatchDetectionJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='任务名称')),
                ('description', models.TextField(blank=True, verbose_name='任务描述')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('processing', '处理中'), ('completed', '已完成'), ('failed', '失败')], default='pending', max_length=20, verbose_name='任务状态')),
                ('detection_mode', models.CharField(choices=[('standard', '标准检测'), ('zero_shot', '零样本检测')], default='standard', max_length=20, verbose_name='检测模式')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('total_files', models.IntegerField(default=0, verbose_name='总文件数')),
                ('processed_files', models.IntegerField(default=0, verbose_name='已处理文件数')),
                ('phishing_count', models.IntegerField(default=0, verbose_name='钓鱼邮件数量')),
                ('safe_count', models.IntegerField(default=0, verbose_name='安全邮件数量')),
                ('avg_confidence', models.FloatField(default=0.0, verbose_name='平均置信度')),
                ('high_risk_count', models.IntegerField(default=0, verbose_name='高危邮件数量')),
                ('medium_risk_count', models.IntegerField(default=0, verbose_name='中危邮件数量')),
                ('low_risk_count', models.IntegerField(default=0, verbose_name='低危邮件数量')),
                ('safe_risk_count', models.IntegerField(default=0, verbose_name='安全邮件数量')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户')),
            ],
            options={
                'verbose_name': '批量检测任务',
                'verbose_name_plural': '批量检测任务',
                'ordering': ['-created_time'],
            },
        ),
        migrations.AddField(
            model_name='detectionresult',
            name='batch_job',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='detection_results', to='detection.batchdetectionjob', verbose_name='批量检测任务'),
        ),
    ]
