from rest_framework import status, viewsets
from rest_framework.decorators import api_view, action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from django.db.models import Count, Avg
from django.utils import timezone
from datetime import timedelta
import time
import logging
import json
import zipfile
import os
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import threading
from concurrent.futures import ThreadPoolExecutor
import uuid

from .models import EmailFile, DetectionResult, SystemConfig, BatchDetectionJob
from .serializers import (
    EmailFileSerializer, DetectionResultSerializer, DetectionRequestSerializer,
    SystemConfigSerializer, DetectionHistorySerializer, StatisticsSerializer,
    BatchDetectionJobSerializer, BatchDetectionJobDetailSerializer, DetectionHistoryMixedSerializer
)

# 配置日志
logger = logging.getLogger(__name__)

logger = logging.getLogger(__name__)


@api_view(['POST'])
def upload_email(request):
    """邮件文件上传接口"""
    serializer = DetectionRequestSerializer(data=request.data)

    if serializer.is_valid():
        file = serializer.validated_data['file']

        # 创建邮件文件记录
        email_file = EmailFile.objects.create(
            filename=file.name,
            file_path=file,
            file_size=file.size,
            user=request.user if request.user.is_authenticated else None
        )

        return Response({
            'id': email_file.id,
            'filename': email_file.filename,
            'message': '文件上传成功'
        }, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
def detect_phishing(request):
    """钓鱼邮件检测接口"""
    serializer = DetectionRequestSerializer(data=request.data)

    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    file = serializer.validated_data['file']
    detection_mode = serializer.validated_data['detection_mode']

    start_time = time.time()

    try:
        # 创建邮件文件记录
        email_file = EmailFile.objects.create(
            filename=file.name,
            file_path=file,
            file_size=file.size,
            user=request.user if request.user.is_authenticated else None
        )

        # 调用AI模型进行检测
        detection_result = _perform_ai_detection(email_file, detection_mode)

        processing_time = time.time() - start_time

        # 创建检测结果记录
        detection_record = DetectionResult.objects.create(
            email_file=email_file,
            is_phishing=detection_result['is_phishing'],
            confidence=detection_result['confidence'],
            risk_level=detection_result['risk_level'],
            processing_time=processing_time,
            text_features=detection_result['text_features'],
            url_features=detection_result['url_features'],
            attachment_features=detection_result['attachment_features'],
            metadata_features=detection_result['metadata_features'],
            prediction_details=detection_result['prediction_details']
        )

        serializer = DetectionResultSerializer(detection_record)
        return Response(serializer.data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"检测过程中发生错误: {str(e)}")
        return Response({
            'error': '检测过程中发生错误',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _perform_ai_detection(email_file, detection_mode):
    """
    执行AI模型检测
    集成零样本钓鱼邮件检测器
    """
    from .ai_integration import ai_manager

    try:
        # 读取邮件内容
        email_content = _read_email_content(email_file)

        # 提取邮件元数据
        email_metadata = _extract_email_metadata(email_file)

        # 调用AI模型进行检测
        result = ai_manager.detect_phishing(
            email_content=email_content,
            email_metadata=email_metadata,
            detection_mode=detection_mode
        )

        return result

    except Exception as e:
        logger.error(f"AI detection failed: {str(e)}")
        # 如果AI检测失败，回退到启发式检测
        return _fallback_heuristic_detection(email_file, detection_mode)


def _read_email_content(email_file):
    """读取邮件文件内容"""
    try:
        with open(email_file.file_path.path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        return content
    except Exception as e:
        logger.error(f"Failed to read email content: {e}")
        return ""


def _extract_email_metadata(email_file):
    """提取邮件元数据"""
    # 简化实现：从文件名和基本信息提取
    metadata = {
        'filename': email_file.filename,
        'file_size': email_file.file_size,
        'upload_time': email_file.upload_time.isoformat(),
        'sender': '',  # 需要从邮件内容解析
        'attachments': [],  # 需要从邮件内容解析
        'headers': {},  # 需要从邮件内容解析
        'spf_valid': False,
        'dkim_valid': False,
        'dmarc_valid': False,
        'domain_age': 365
    }

    return metadata


def _fallback_heuristic_detection(email_file, detection_mode):
    """
    启发式检测（AI模型不可用时的回退方案）
    """
    import random

    try:
        email_content = _read_email_content(email_file)

        # 简单的启发式规则
        suspicious_indicators = 0

        # 检查可疑词汇
        suspicious_words = ['urgent', 'verify', 'click here', 'suspend', 'expire', 'confirm']
        content_lower = email_content.lower()
        found_words = [word for word in suspicious_words if word in content_lower]
        suspicious_indicators += len(found_words)

        # 检查URL
        import re
        urls = re.findall(r'http[s]?://[^\s]+', email_content)
        suspicious_indicators += len(urls)

        # 计算置信度
        base_confidence = min(suspicious_indicators * 0.15, 0.85)
        confidence = base_confidence + random.uniform(-0.1, 0.1)
        confidence = max(0.1, min(0.95, confidence))

        is_phishing = confidence > 0.5

        if confidence >= 0.8:
            risk_level = 'high'
        elif confidence >= 0.6:
            risk_level = 'medium'
        elif confidence >= 0.4:
            risk_level = 'low'
        else:
            risk_level = 'safe'

        return {
            'is_phishing': is_phishing,
            'confidence': confidence,
            'risk_level': risk_level,
            'text_features': {
                'suspicious_words': found_words,
                'suspicious_word_count': len(found_words),
                'text_length': len(email_content),
                'word_count': len(email_content.split())
            },
            'url_features': {
                'suspicious_urls': urls,
                'url_count': len(urls),
                'shortened_urls': 0
            },
            'attachment_features': {
                'has_attachments': False,
                'attachment_types': [],
                'suspicious_attachments': []
            },
            'metadata_features': {
                'sender_reputation': random.uniform(0.3, 0.8),
                'domain_age': random.randint(30, 3650),
                'spf_valid': random.choice([True, False])
            },
            'prediction_details': {
                'detection_mode': detection_mode,
                'model_type': 'heuristic_fallback',
                'suspicious_indicators': suspicious_indicators,
                'fallback_reason': 'AI model unavailable'
            }
        }

    except Exception as e:
        logger.error(f"Heuristic detection failed: {e}")
        # 最后的回退：返回安全结果
        return {
            'is_phishing': False,
            'confidence': 0.3,
            'risk_level': 'safe',
            'text_features': {},
            'url_features': {},
            'attachment_features': {},
            'metadata_features': {},
            'prediction_details': {
                'detection_mode': detection_mode,
                'model_type': 'error_fallback',
                'error': str(e)
            }
        }


@api_view(['GET'])
def get_detection_result(request, result_id):
    """获取检测结果详情"""
    try:
        result = DetectionResult.objects.get(id=result_id)
        serializer = DetectionResultSerializer(result)
        return Response(serializer.data)
    except DetectionResult.DoesNotExist:
        return Response({
            'error': '检测结果不存在'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
def get_detection_history(request):
    """获取检测历史（混合显示单个检测和批量检测）"""
    try:
        page_size = int(request.GET.get('page_size', 20))
        page = int(request.GET.get('page', 1))
        risk_level = request.GET.get('risk_level')
        is_phishing = request.GET.get('is_phishing')

        mixed_results = []

        # 获取批量检测任务（作为主要记录）
        batch_jobs_queryset = BatchDetectionJob.objects.all()

        # 应用筛选条件到批量任务
        if is_phishing is not None:
            is_phishing_bool = is_phishing.lower() == 'true'
            if is_phishing_bool:
                batch_jobs_queryset = batch_jobs_queryset.filter(phishing_count__gt=0)
            else:
                batch_jobs_queryset = batch_jobs_queryset.filter(phishing_count=0)

        # 获取单个检测记录（不属于任何批量任务的）
        single_detections_queryset = DetectionResult.objects.filter(batch_job__isnull=True)

        if risk_level:
            single_detections_queryset = single_detections_queryset.filter(risk_level=risk_level)
        if is_phishing is not None:
            is_phishing_bool = is_phishing.lower() == 'true'
            single_detections_queryset = single_detections_queryset.filter(is_phishing=is_phishing_bool)

        # 合并结果并按时间排序
        for batch_job in batch_jobs_queryset:
            mixed_results.append({
                'type': 'batch',
                'data': BatchDetectionJobSerializer(batch_job).data,
                'sort_time': batch_job.created_time
            })

        for single_detection in single_detections_queryset:
            mixed_results.append({
                'type': 'single',
                'data': DetectionHistorySerializer(single_detection).data,
                'sort_time': single_detection.detection_time
            })

        # 按时间排序（最新的在前）
        mixed_results.sort(key=lambda x: x['sort_time'], reverse=True)

        # 分页
        total_count = len(mixed_results)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_results = mixed_results[start:end]

        # 移除排序用的时间字段
        for result in paginated_results:
            del result['sort_time']

        return Response({
            'results': paginated_results,
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'total_pages': (total_count + page_size - 1) // page_size
        })

    except Exception as e:
        logger.error(f"获取检测历史失败: {e}")
        return Response({
            'error': '获取检测历史失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_batch_detection_detail(request, batch_id):
    """获取批量检测任务详情"""
    try:
        batch_job = BatchDetectionJob.objects.get(id=batch_id)
        serializer = BatchDetectionJobDetailSerializer(batch_job)
        return Response(serializer.data)
    except BatchDetectionJob.DoesNotExist:
        return Response({
            'error': '批量检测任务不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"获取批量检测详情失败: {e}")
        return Response({
            'error': '获取批量检测详情失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_statistics(request):
    """获取统计数据"""
    # 时间范围筛选
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)

    results = DetectionResult.objects.filter(detection_time__gte=start_date)

    total_detections = results.count()
    phishing_count = results.filter(is_phishing=True).count()
    safe_count = total_detections - phishing_count

    # 计算统计指标
    stats = {
        'total_detections': total_detections,
        'phishing_count': phishing_count,
        'safe_count': safe_count,
        'phishing_rate': phishing_count / total_detections if total_detections > 0 else 0,
        'avg_confidence': results.aggregate(Avg('confidence'))['confidence__avg'] or 0,
        'avg_processing_time': results.aggregate(Avg('processing_time'))['processing_time__avg'] or 0,
    }

    # 风险等级分布
    risk_distribution = results.values('risk_level').annotate(count=Count('risk_level'))
    stats['risk_level_distribution'] = {item['risk_level']: item['count'] for item in risk_distribution}

    # 每日检测数量
    daily_stats = []
    for i in range(days):
        date = start_date + timedelta(days=i)
        count = results.filter(
            detection_time__date=date.date()
        ).count()
        daily_stats.append({
            'date': date.strftime('%Y-%m-%d'),
            'count': count
        })

    stats['daily_detections'] = daily_stats

    serializer = StatisticsSerializer(stats)
    return Response(serializer.data)


@api_view(['GET', 'POST'])
def system_config(request):
    """系统配置管理"""
    if request.method == 'GET':
        configs = SystemConfig.objects.all()
        serializer = SystemConfigSerializer(configs, many=True)
        return Response(serializer.data)

    elif request.method == 'POST':
        serializer = SystemConfigSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# 批量检测相关的全局变量
batch_jobs = {}  # 存储批量检测任务状态


@api_view(['POST'])
def batch_upload(request):
    """
    批量上传邮件文件
    支持ZIP文件或多个单独文件
    """
    try:
        # 生成批量任务ID
        batch_id = str(uuid.uuid4())

        # 获取上传的文件
        uploaded_files = request.FILES.getlist('files')
        zip_file = request.FILES.get('zip_file')

        email_files = []

        if zip_file:
            # 处理ZIP文件
            email_files = _extract_zip_file(zip_file, batch_id)
        elif uploaded_files:
            # 处理多个单独文件
            email_files = _process_individual_files(uploaded_files, batch_id)
        else:
            return Response({
                'error': '请上传邮件文件或ZIP压缩包'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建批量检测任务记录
        batch_job = BatchDetectionJob.objects.create(
            name=f"批量检测任务_{timezone.now().strftime('%Y%m%d_%H%M%S')}",
            description=f"包含 {len(email_files)} 个邮件文件的批量检测",
            total_files=len(email_files),
            user=request.user if request.user.is_authenticated else None
        )

        # 初始化批量任务状态（内存中）
        batch_jobs[str(batch_job.id)] = {
            'status': 'uploaded',
            'total_files': len(email_files),
            'processed_files': 0,
            'results': [],
            'start_time': timezone.now(),
            'end_time': None,
            'error': None,
            'email_files': email_files,
            'batch_job_id': batch_job.id
        }

        return Response({
            'batch_id': str(batch_job.id),
            'total_files': len(email_files),
            'message': f'成功上传 {len(email_files)} 个邮件文件'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"批量上传失败: {str(e)}")
        return Response({
            'error': f'批量上传失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def batch_detect(request):
    """
    开始批量检测
    """
    try:
        batch_id = request.data.get('batch_id')
        detection_mode = request.data.get('detection_mode', 'standard')

        if not batch_id or batch_id not in batch_jobs:
            return Response({
                'error': '无效的批量任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 更新数据库中的任务状态
        try:
            batch_job = BatchDetectionJob.objects.get(id=batch_id)
            batch_job.status = 'processing'
            batch_job.detection_mode = detection_mode
            batch_job.start_time = timezone.now()
            batch_job.save()
        except BatchDetectionJob.DoesNotExist:
            return Response({
                'error': '批量检测任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 更新内存中的任务状态
        batch_jobs[batch_id]['status'] = 'processing'
        batch_jobs[batch_id]['detection_mode'] = detection_mode

        # 在后台线程中执行批量检测
        thread = threading.Thread(
            target=_process_batch_detection,
            args=(batch_id, detection_mode)
        )
        thread.daemon = True
        thread.start()

        return Response({
            'message': '批量检测已开始',
            'batch_id': batch_id
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"批量检测启动失败: {str(e)}")
        return Response({
            'error': f'批量检测启动失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def batch_status(request, batch_id):
    """
    获取批量检测状态
    """
    try:
        if batch_id not in batch_jobs:
            return Response({
                'error': '批量任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        job = batch_jobs[batch_id]

        # 计算进度百分比
        progress = 0
        if job['total_files'] > 0:
            progress = (job['processed_files'] / job['total_files']) * 100

        # 计算处理速度
        processing_speed = 0
        if job['processed_files'] > 0 and job['start_time']:
            elapsed_time = (timezone.now() - job['start_time']).total_seconds()
            if elapsed_time > 0:
                processing_speed = job['processed_files'] / elapsed_time

        return Response({
            'batch_id': batch_id,
            'status': job['status'],
            'total_files': job['total_files'],
            'processed_files': job['processed_files'],
            'progress': round(progress, 2),
            'processing_speed': round(processing_speed, 2),
            'start_time': job['start_time'],
            'end_time': job['end_time'],
            'error': job.get('error')
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"获取批量状态失败: {str(e)}")
        return Response({
            'error': f'获取批量状态失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def batch_results(request, batch_id):
    """
    获取批量检测结果
    """
    try:
        if batch_id not in batch_jobs:
            return Response({
                'error': '批量任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        job = batch_jobs[batch_id]

        # 统计结果
        total_results = len(job['results'])
        phishing_count = sum(1 for result in job['results'] if result['is_phishing'])
        normal_count = total_results - phishing_count

        # 计算平均置信度
        avg_confidence = 0
        if total_results > 0:
            avg_confidence = sum(result['confidence'] for result in job['results']) / total_results

        return Response({
            'batch_id': batch_id,
            'status': job['status'],
            'summary': {
                'total_emails': total_results,
                'phishing_emails': phishing_count,
                'normal_emails': normal_count,
                'phishing_rate': round((phishing_count / total_results * 100), 2) if total_results > 0 else 0,
                'average_confidence': round(avg_confidence, 3)
            },
            'results': job['results']
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"获取批量结果失败: {str(e)}")
        return Response({
            'error': f'获取批量结果失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def export_batch_results(request, batch_id):
    """
    导出批量检测结果为CSV文件
    """
    try:
        if batch_id not in batch_jobs:
            return Response({
                'error': '批量任务不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        job = batch_jobs[batch_id]

        # 创建CSV内容
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow([
            '文件名', '检测结果', '置信度', '风险等级',
            '可疑词汇数', 'URL数量', '检测模式', '处理时间'
        ])

        # 写入数据
        for result in job['results']:
            writer.writerow([
                result.get('filename', ''),
                '钓鱼邮件' if result['is_phishing'] else '正常邮件',
                f"{result['confidence']:.3f}",
                result['risk_level'],
                result.get('text_features', {}).get('suspicious_word_count', 0),
                result.get('url_features', {}).get('url_count', 0),
                job.get('detection_mode', 'standard'),
                result.get('processing_time', 0)
            ])

        # 创建HTTP响应
        response = HttpResponse(
            output.getvalue(),
            content_type='text/csv; charset=utf-8'
        )
        response['Content-Disposition'] = f'attachment; filename="batch_results_{batch_id[:8]}.csv"'

        return response

    except Exception as e:
        logger.error(f"导出批量结果失败: {str(e)}")
        return Response({
            'error': f'导出批量结果失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 辅助函数
def _extract_zip_file(zip_file, batch_id):
    """从ZIP文件中提取邮件文件"""
    email_files = []

    try:
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            # 创建临时目录
            temp_dir = f'/tmp/batch_{batch_id}'
            os.makedirs(temp_dir, exist_ok=True)

            # 提取所有文件
            zip_ref.extractall(temp_dir)

            # 遍历提取的文件
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    if file.lower().endswith(('.txt', '.eml', '.msg')):
                        file_path = os.path.join(root, file)
                        email_files.append({
                            'filename': file,
                            'path': file_path,
                            'size': os.path.getsize(file_path)
                        })

    except Exception as e:
        logger.error(f"ZIP文件提取失败: {e}")
        raise e

    return email_files


def _process_individual_files(uploaded_files, batch_id):
    """处理单独上传的文件"""
    email_files = []

    try:
        # 创建临时目录
        temp_dir = f'/tmp/batch_{batch_id}'
        os.makedirs(temp_dir, exist_ok=True)

        for uploaded_file in uploaded_files:
            # 保存文件到临时目录
            file_path = os.path.join(temp_dir, uploaded_file.name)
            with open(file_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)

            email_files.append({
                'filename': uploaded_file.name,
                'path': file_path,
                'size': uploaded_file.size
            })

    except Exception as e:
        logger.error(f"文件处理失败: {e}")
        raise e

    return email_files


def _process_batch_detection(batch_id, detection_mode):
    """
    批量检测处理函数（在后台线程中运行）
    """
    try:
        job = batch_jobs[batch_id]
        email_files = job['email_files']

        # 获取数据库中的批量任务记录
        batch_job = BatchDetectionJob.objects.get(id=batch_id)

        # 使用线程池并行处理
        max_workers = min(10, len(email_files))  # 最多10个并发线程

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有检测任务
            future_to_file = {}
            for email_file in email_files:
                future = executor.submit(_detect_single_email_for_batch, email_file, detection_mode, batch_job)
                future_to_file[future] = email_file

            # 收集结果
            for future in future_to_file:
                try:
                    result = future.result(timeout=30)  # 30秒超时
                    email_file = future_to_file[future]

                    # 保存结果到内存
                    job['results'].append(result)
                    job['processed_files'] += 1

                except Exception as e:
                    logger.error(f"检测文件 {future_to_file[future]['filename']} 失败: {e}")
                    # 添加错误结果
                    job['results'].append({
                        'filename': future_to_file[future]['filename'],
                        'is_phishing': False,
                        'confidence': 0.0,
                        'risk_level': 'error',
                        'error': str(e)
                    })
                    job['processed_files'] += 1

        # 更新数据库中的任务状态和统计信息
        batch_job.status = 'completed'
        batch_job.end_time = timezone.now()
        batch_job.update_statistics()  # 更新统计信息

        # 更新内存中的任务状态
        job['status'] = 'completed'
        job['end_time'] = timezone.now()

        logger.info(f"批量检测完成: {batch_id}, 处理了 {job['processed_files']} 个文件")

    except Exception as e:
        logger.error(f"批量检测处理失败: {e}")

        # 更新数据库状态
        try:
            batch_job = BatchDetectionJob.objects.get(id=batch_id)
            batch_job.status = 'failed'
            batch_job.error_message = str(e)
            batch_job.end_time = timezone.now()
            batch_job.save()
        except:
            pass

        # 更新内存状态
        job['status'] = 'failed'
        job['error'] = str(e)
        job['end_time'] = timezone.now()


def _detect_single_email(email_file, detection_mode):
    """
    检测单个邮件文件
    """
    try:
        # 读取邮件内容
        with open(email_file['path'], 'r', encoding='utf-8', errors='ignore') as f:
            email_content = f.read()

        # 提取邮件元数据
        email_metadata = {
            'filename': email_file['filename'],
            'file_size': email_file['size'],
            'sender': '',
            'attachments': [],
            'headers': {}
        }

        # 调用检测函数
        start_time = time.time()
        result = _perform_ai_detection_content(email_content, email_metadata, detection_mode)
        processing_time = time.time() - start_time

        # 添加处理时间
        result['processing_time'] = round(processing_time, 3)

        return result

    except Exception as e:
        logger.error(f"单个邮件检测失败: {e}")
        return {
            'is_phishing': False,
            'confidence': 0.0,
            'risk_level': 'error',
            'error': str(e),
            'processing_time': 0
        }


def _detect_single_email_with_history(email_file, detection_mode):
    """
    检测单个邮件文件并保存到检测历史
    """
    try:
        # 读取邮件内容
        with open(email_file['path'], 'r', encoding='utf-8', errors='ignore') as f:
            email_content = f.read()

        # 创建EmailFile记录
        email_file_obj = EmailFile.objects.create(
            filename=email_file['filename'],
            file_size=email_file['size'],
            file_path=email_file['path']
        )

        # 提取邮件元数据
        email_metadata = {
            'filename': email_file['filename'],
            'file_size': email_file['size'],
            'sender': '',
            'attachments': [],
            'headers': {}
        }

        # 调用检测函数
        start_time = time.time()
        detection_result = _perform_ai_detection_content(email_content, email_metadata, detection_mode)
        processing_time = time.time() - start_time

        # 创建DetectionResult记录
        detection_record = DetectionResult.objects.create(
            email_file=email_file_obj,
            is_phishing=detection_result['is_phishing'],
            confidence=detection_result['confidence'],
            risk_level=detection_result['risk_level'],
            processing_time=processing_time,
            text_features=detection_result.get('text_features', {}),
            url_features=detection_result.get('url_features', {}),
            attachment_features=detection_result.get('attachment_features', {}),
            metadata_features=detection_result.get('metadata_features', {}),
            prediction_details=detection_result.get('prediction_details', {})
        )

        # 返回结果（包含数据库ID）
        result = {
            'id': str(detection_record.id),
            'filename': email_file['filename'],
            'is_phishing': detection_result['is_phishing'],
            'confidence': detection_result['confidence'],
            'risk_level': detection_result['risk_level'],
            'processing_time': round(processing_time, 3),
            'text_features': detection_result.get('text_features', {}),
            'url_features': detection_result.get('url_features', {}),
            'attachment_features': detection_result.get('attachment_features', {}),
            'metadata_features': detection_result.get('metadata_features', {}),
            'prediction_details': detection_result.get('prediction_details', {})
        }

        return result

    except Exception as e:
        logger.error(f"单个邮件检测失败: {e}")
        return {
            'filename': email_file['filename'],
            'is_phishing': False,
            'confidence': 0.0,
            'risk_level': 'error',
            'error': str(e),
            'processing_time': 0
        }


def _detect_single_email_for_batch(email_file, detection_mode, batch_job):
    """
    检测单个邮件文件并保存到检测历史（批量检测版本）
    """
    try:
        # 读取邮件内容
        with open(email_file['path'], 'r', encoding='utf-8', errors='ignore') as f:
            email_content = f.read()

        # 创建EmailFile记录
        email_file_obj = EmailFile.objects.create(
            filename=email_file['filename'],
            file_size=email_file['size'],
            file_path=email_file['path']
        )

        # 提取邮件元数据
        email_metadata = {
            'filename': email_file['filename'],
            'file_size': email_file['size'],
            'sender': '',
            'attachments': [],
            'headers': {}
        }

        # 调用检测函数
        start_time = time.time()
        detection_result = _perform_ai_detection_content(email_content, email_metadata, detection_mode)
        processing_time = time.time() - start_time

        # 创建DetectionResult记录（关联到批量任务）
        detection_record = DetectionResult.objects.create(
            email_file=email_file_obj,
            batch_job=batch_job,  # 关联到批量任务
            is_phishing=detection_result['is_phishing'],
            confidence=detection_result['confidence'],
            risk_level=detection_result['risk_level'],
            processing_time=processing_time,
            text_features=detection_result.get('text_features', {}),
            url_features=detection_result.get('url_features', {}),
            attachment_features=detection_result.get('attachment_features', {}),
            metadata_features=detection_result.get('metadata_features', {}),
            prediction_details=detection_result.get('prediction_details', {})
        )

        # 返回结果（包含数据库ID）
        result = {
            'id': str(detection_record.id),
            'filename': email_file['filename'],
            'is_phishing': detection_result['is_phishing'],
            'confidence': detection_result['confidence'],
            'risk_level': detection_result['risk_level'],
            'processing_time': round(processing_time, 3),
            'text_features': detection_result.get('text_features', {}),
            'url_features': detection_result.get('url_features', {}),
            'attachment_features': detection_result.get('attachment_features', {}),
            'metadata_features': detection_result.get('metadata_features', {}),
            'prediction_details': detection_result.get('prediction_details', {})
        }

        return result

    except Exception as e:
        logger.error(f"单个邮件检测失败: {e}")
        return {
            'filename': email_file['filename'],
            'is_phishing': False,
            'confidence': 0.0,
            'risk_level': 'error',
            'error': str(e),
            'processing_time': 0
        }


def _perform_ai_detection_content(email_content, email_metadata, detection_mode):
    """
    执行AI检测（内容版本，不需要EmailFile对象）
    """
    from .ai_integration import ai_manager

    try:
        # 调用AI模型进行检测
        result = ai_manager.detect_phishing(
            email_content=email_content,
            email_metadata=email_metadata,
            detection_mode=detection_mode
        )

        return result

    except Exception as e:
        logger.error(f"AI检测失败: {str(e)}")
        # 如果AI检测失败，回退到启发式检测
        return _fallback_heuristic_detection_content(email_content, detection_mode)


def _fallback_heuristic_detection_content(email_content, detection_mode):
    """
    启发式检测（内容版本）
    """
    import random

    try:
        # 简单的启发式规则
        suspicious_indicators = 0

        # 检查可疑词汇
        suspicious_words = ['urgent', 'verify', 'click here', 'suspend', 'expire', 'confirm']
        content_lower = email_content.lower()
        found_words = [word for word in suspicious_words if word in content_lower]
        suspicious_indicators += len(found_words)

        # 检查URL
        import re
        urls = re.findall(r'http[s]?://[^\s]+', email_content)
        suspicious_indicators += len(urls)

        # 计算置信度
        base_confidence = min(suspicious_indicators * 0.15, 0.85)
        confidence = base_confidence + random.uniform(-0.1, 0.1)
        confidence = max(0.1, min(0.95, confidence))

        is_phishing = confidence > 0.5

        if confidence >= 0.8:
            risk_level = 'high'
        elif confidence >= 0.6:
            risk_level = 'medium'
        elif confidence >= 0.4:
            risk_level = 'low'
        else:
            risk_level = 'safe'

        return {
            'is_phishing': is_phishing,
            'confidence': confidence,
            'risk_level': risk_level,
            'text_features': {
                'suspicious_words': found_words,
                'suspicious_word_count': len(found_words),
                'text_length': len(email_content),
                'word_count': len(email_content.split())
            },
            'url_features': {
                'suspicious_urls': urls,
                'url_count': len(urls),
                'shortened_urls': 0
            },
            'attachment_features': {
                'has_attachments': False,
                'attachment_types': [],
                'suspicious_attachments': []
            },
            'metadata_features': {
                'sender_reputation': random.uniform(0.3, 0.8),
                'domain_age': random.randint(30, 3650),
                'spf_valid': random.choice([True, False])
            },
            'prediction_details': {
                'detection_mode': detection_mode,
                'model_type': 'heuristic_fallback',
                'suspicious_indicators': suspicious_indicators,
                'fallback_reason': 'AI model unavailable',
                'feature_importance': {
                    'text': 0.4,
                    'url': 0.3,
                    'metadata': 0.2,
                    'attachment': 0.1
                },
                'model_version': 'v2.0'
            }
        }

    except Exception as e:
        logger.error(f"启发式检测失败: {e}")
        # 最后的回退：返回安全结果
        return {
            'is_phishing': False,
            'confidence': 0.3,
            'risk_level': 'safe',
            'text_features': {},
            'url_features': {},
            'attachment_features': {},
            'metadata_features': {},
            'prediction_details': {
                'detection_mode': detection_mode,
                'model_type': 'error_fallback',
                'error': str(e)
            }
        }
