from django.db import models
from django.contrib.auth.models import User
import uuid


class EmailFile(models.Model):
    """邮件文件模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    filename = models.CharField(max_length=255, verbose_name="文件名")
    file_path = models.FileField(upload_to='emails/', verbose_name="文件路径")
    file_size = models.IntegerField(verbose_name="文件大小(字节)")
    upload_time = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name="上传用户")

    class Meta:
        verbose_name = "邮件文件"
        verbose_name_plural = "邮件文件"
        ordering = ['-upload_time']

    def __str__(self):
        return self.filename


class BatchDetectionJob(models.Model):
    """批量检测任务模型"""
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]

    DETECTION_MODES = [
        ('standard', '标准检测'),
        ('zero_shot', '零样本检测'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, verbose_name="任务名称")
    description = models.TextField(blank=True, verbose_name="任务描述")

    # 任务状态
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="任务状态")
    detection_mode = models.CharField(max_length=20, choices=DETECTION_MODES, default='standard', verbose_name="检测模式")

    # 时间信息
    created_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")

    # 统计信息
    total_files = models.IntegerField(default=0, verbose_name="总文件数")
    processed_files = models.IntegerField(default=0, verbose_name="已处理文件数")
    phishing_count = models.IntegerField(default=0, verbose_name="钓鱼邮件数量")
    safe_count = models.IntegerField(default=0, verbose_name="安全邮件数量")

    # 结果统计
    avg_confidence = models.FloatField(default=0.0, verbose_name="平均置信度")
    high_risk_count = models.IntegerField(default=0, verbose_name="高危邮件数量")
    medium_risk_count = models.IntegerField(default=0, verbose_name="中危邮件数量")
    low_risk_count = models.IntegerField(default=0, verbose_name="低危邮件数量")
    safe_risk_count = models.IntegerField(default=0, verbose_name="安全邮件数量")

    # 用户信息
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name="创建用户")

    # 错误信息
    error_message = models.TextField(blank=True, verbose_name="错误信息")

    class Meta:
        verbose_name = "批量检测任务"
        verbose_name_plural = "批量检测任务"
        ordering = ['-created_time']

    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"

    @property
    def progress_percentage(self):
        """计算进度百分比"""
        if self.total_files == 0:
            return 0
        return round((self.processed_files / self.total_files) * 100, 1)

    @property
    def phishing_percentage(self):
        """计算钓鱼邮件比例"""
        if self.processed_files == 0:
            return 0
        return round((self.phishing_count / self.processed_files) * 100, 1)

    def update_statistics(self):
        """更新统计信息"""
        results = self.detection_results.all()

        self.processed_files = results.count()
        self.phishing_count = results.filter(is_phishing=True).count()
        self.safe_count = results.filter(is_phishing=False).count()

        # 计算平均置信度
        if results.exists():
            self.avg_confidence = results.aggregate(models.Avg('confidence'))['confidence__avg'] or 0.0

        # 统计风险等级
        self.high_risk_count = results.filter(risk_level='high').count()
        self.medium_risk_count = results.filter(risk_level='medium').count()
        self.low_risk_count = results.filter(risk_level='low').count()
        self.safe_risk_count = results.filter(risk_level='safe').count()

        self.save()


class DetectionResult(models.Model):
    """检测结果模型"""
    RISK_LEVELS = [
        ('safe', '安全'),
        ('low', '低危'),
        ('medium', '中危'),
        ('high', '高危'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email_file = models.OneToOneField(EmailFile, on_delete=models.CASCADE, verbose_name="邮件文件")
    batch_job = models.ForeignKey(BatchDetectionJob, on_delete=models.CASCADE, null=True, blank=True,
                                 related_name='detection_results', verbose_name="批量检测任务")
    is_phishing = models.BooleanField(verbose_name="是否为钓鱼邮件")
    confidence = models.FloatField(verbose_name="置信度")
    risk_level = models.CharField(max_length=10, choices=RISK_LEVELS, verbose_name="风险等级")
    detection_time = models.DateTimeField(auto_now_add=True, verbose_name="检测时间")
    processing_time = models.FloatField(verbose_name="处理时间(秒)")

    # 特征分析结果
    text_features = models.JSONField(default=dict, verbose_name="文本特征")
    url_features = models.JSONField(default=dict, verbose_name="URL特征")
    attachment_features = models.JSONField(default=dict, verbose_name="附件特征")
    metadata_features = models.JSONField(default=dict, verbose_name="元数据特征")

    # 模型预测详情
    model_version = models.CharField(max_length=50, default="v1.0", verbose_name="模型版本")
    prediction_details = models.JSONField(default=dict, verbose_name="预测详情")

    class Meta:
        verbose_name = "检测结果"
        verbose_name_plural = "检测结果"
        ordering = ['-detection_time']

    def __str__(self):
        return f"{self.email_file.filename} - {self.get_risk_level_display()}"


class SystemConfig(models.Model):
    """系统配置模型"""
    key = models.CharField(max_length=100, unique=True, verbose_name="配置键")
    value = models.TextField(verbose_name="配置值")
    description = models.TextField(blank=True, verbose_name="描述")
    updated_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "系统配置"
        verbose_name_plural = "系统配置"

    def __str__(self):
        return self.key
