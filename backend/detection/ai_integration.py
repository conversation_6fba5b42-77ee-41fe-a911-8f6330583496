"""
AI模型集成模块
将零样本钓鱼邮件检测器集成到Django后端

功能：
1. 初始化和管理AI模型
2. 提供检测接口
3. 处理模型预测结果
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import torch

# 添加ai_models到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from ai_models.zero_shot_detector import ZeroShotPhishingDetector
    from ai_models.adversarial_learning import AdversarialGenerator, AdversarialTrainer
    from ai_models.meta_learning import MAMLOptimizer, MetaLearner
    from ai_models.dynamic_threshold import DynamicThresholdManager
    AI_MODELS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"AI models not available: {e}")
    AI_MODELS_AVAILABLE = False
except Exception as e:
    logging.warning(f"AI models initialization failed: {e}")
    AI_MODELS_AVAILABLE = False


class SimplePhishingDetector:
    """简单的钓鱼邮件检测器（回退方案）"""

    def __init__(self):
        # 钓鱼邮件常见关键词
        self.phishing_keywords = [
            'urgent', 'verify', 'suspend', 'click here', 'act now',
            'limited time', 'confirm identity', 'security alert',
            '紧急', '验证', '暂停', '点击这里', '立即行动',
            '有限时间', '确认身份', '安全警报', '账户异常'
        ]

        # 可疑域名模式
        self.suspicious_patterns = [
            'secure-', 'verify-', 'account-', 'user-', 'safety-',
            'security-', 'bank-', 'payment-', 'confirm-'
        ]

    def detect(self, email_content: str) -> Dict[str, Any]:
        """简单的基于规则的检测"""
        content_lower = email_content.lower()

        # 计算钓鱼关键词得分
        keyword_score = 0
        found_keywords = []
        for keyword in self.phishing_keywords:
            if keyword.lower() in content_lower:
                keyword_score += 1
                found_keywords.append(keyword)

        # 检查可疑域名
        domain_score = 0
        found_domains = []
        for pattern in self.suspicious_patterns:
            if pattern in content_lower:
                domain_score += 1
                found_domains.append(pattern)

        # 计算总体风险分数
        total_score = keyword_score * 0.6 + domain_score * 0.4
        max_possible_score = len(self.phishing_keywords) * 0.6 + len(self.suspicious_patterns) * 0.4
        risk_score = min(total_score / max_possible_score, 1.0) if max_possible_score > 0 else 0.0

        # 判断是否为钓鱼邮件
        is_phishing = risk_score > 0.3
        confidence = risk_score

        return {
            'is_phishing': is_phishing,
            'confidence': confidence,
            'risk_score': risk_score,
            'details': {
                'found_keywords': found_keywords,
                'found_domains': found_domains,
                'keyword_score': keyword_score,
                'domain_score': domain_score,
                'detection_method': 'rule_based_fallback'
            }
        }


class AIModelManager:
    """
    AI模型管理器
    负责初始化、加载和管理AI模型
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.detector = None
        self.is_initialized = False
        self.fallback_detector = SimplePhishingDetector()
        
        # 模型配置
        self.config = {
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'text_config': {
                'model_name': 'bert-base-uncased',
                'max_length': 512
            },
            'url_config': {},
            'metadata_config': {},
            'gcn_config': {
                'input_dim': 768,
                'hidden_dim': 256,
                'output_dim': 2,
                'num_layers': 3,
                'dropout': 0.3
            },
            'vocab_size': 30000,
            'embedding_dim': 768,
            'hidden_dim': 512,
            'inner_lr': 0.01,
            'outer_lr': 0.001,
            'inner_steps': 5,
            'base_threshold': 0.5,
            'sensitivity': 0.3,
            'adaptation_rate': 0.1
        }
        
        # 初始化模型
        if AI_MODELS_AVAILABLE:
            self._initialize_models()
    
    def _initialize_models(self):
        """初始化AI模型"""
        try:
            self.logger.info("Initializing AI models...")
            
            # 创建零样本检测器
            self.detector = ZeroShotPhishingDetector(self.config)
            
            # 检查是否有预训练模型
            model_path = project_root / 'models' / 'zero_shot_detector'
            if model_path.exists():
                self.logger.info(f"Loading pre-trained model from {model_path}")
                self.detector.load_model(str(model_path))
            else:
                self.logger.info("No pre-trained model found, using untrained model")
            
            self.is_initialized = True
            self.logger.info("AI models initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI models: {e}")
            self.is_initialized = False
    
    def detect_phishing(self, email_content: str, email_metadata: Dict,
                       detection_mode: str = 'standard') -> Dict[str, Any]:
        """
        检测钓鱼邮件
        
        Args:
            email_content: 邮件内容
            email_metadata: 邮件元数据
            detection_mode: 检测模式 ('standard' 或 'zero_shot')
            
        Returns:
            检测结果字典
        """
        if not AI_MODELS_AVAILABLE or not self.is_initialized:
            return self._mock_detection(email_content, detection_mode)
        
        try:
            if detection_mode == 'zero_shot':
                # 使用零样本检测
                result = self.detector.detect_zero_shot(email_content, email_metadata)
                
                # 计算特征重要性
                feature_importance = self._calculate_feature_importance(
                    email_content, email_metadata, result['confidence']
                )

                return {
                    'is_phishing': result['is_phishing'],
                    'confidence': result['confidence'],
                    'risk_level': result['risk_level'],
                    'text_features': self._extract_text_features(email_content),
                    'url_features': self._extract_url_features(email_content),
                    'attachment_features': self._extract_attachment_features(email_metadata),
                    'metadata_features': self._extract_metadata_features(email_metadata),
                    'prediction_details': {
                        'detection_mode': detection_mode,
                        'threshold': result['threshold'],
                        'graph_info': result['graph_info'],
                        'diagnostics': result['diagnostics'],
                        'feature_importance': feature_importance,
                        'model_version': 'v2.0'
                    }
                }
            else:
                # 标准检测（简化实现）
                return self._standard_detection(email_content, email_metadata)
                
        except Exception as e:
            self.logger.error(f"AI detection failed: {e}")
            # 使用简单回退检测器
            if hasattr(self, 'fallback_detector'):
                return self._fallback_detection(email_content, detection_mode)
            else:
                return self._mock_detection(email_content, detection_mode)
    
    def _standard_detection(self, email_content: str, email_metadata: Dict) -> Dict[str, Any]:
        """
        标准检测模式
        
        Args:
            email_content: 邮件内容
            email_metadata: 邮件元数据
            
        Returns:
            检测结果
        """
        # 提取特征
        features = self.detector.extract_email_features(email_content, email_metadata)
        
        # 构建图结构
        email_graph = self.detector.build_email_graph(features)
        
        # 使用GCN模型预测
        with torch.no_grad():
            model_input = self.detector._prepare_model_input(email_graph, features)
            prediction_logits = self.detector.gcn_model(model_input)
            prediction_score = torch.softmax(prediction_logits, dim=-1)[0, 1].item()
        
        # 使用固定阈值
        threshold = 0.5
        is_phishing = prediction_score > threshold
        
        # 计算特征重要性
        feature_importance = self._calculate_feature_importance(
            email_content, email_metadata, prediction_score
        )

        return {
            'is_phishing': is_phishing,
            'confidence': prediction_score,
            'risk_level': self._determine_risk_level(prediction_score, threshold),
            'text_features': self._extract_text_features(email_content),
            'url_features': self._extract_url_features(email_content),
            'attachment_features': self._extract_attachment_features(email_metadata),
            'metadata_features': self._extract_metadata_features(email_metadata),
            'prediction_details': {
                'detection_mode': 'standard',
                'threshold': threshold,
                'model_confidence': prediction_score,
                'feature_importance': feature_importance,
                'model_version': 'v2.0'
            }
        }
    
    def _extract_text_features(self, email_content: str) -> Dict[str, Any]:
        """提取文本特征"""
        if not self.detector:
            return {'suspicious_words': [], 'sentiment_score': 0.0}
        
        try:
            patterns = self.detector.text_extractor.extract_suspicious_patterns(email_content)
            return {
                'suspicious_words': self._find_suspicious_words(email_content),
                'suspicious_word_count': patterns['suspicious_word_count'],
                'suspicious_word_ratio': patterns['suspicious_word_ratio'],
                'has_urgent_language': patterns['has_urgent_language'],
                'has_verification_request': patterns['has_verification_request'],
                'text_length': patterns['text_length'],
                'word_count': patterns['word_count']
            }
        except Exception as e:
            self.logger.error(f"Text feature extraction failed: {e}")
            return {'suspicious_words': [], 'sentiment_score': 0.0}
    
    def _extract_url_features(self, email_content: str) -> Dict[str, Any]:
        """提取URL特征"""
        if not self.detector:
            return {'suspicious_urls': [], 'url_count': 0}
        
        try:
            urls = self.detector.url_extractor.extract_urls(email_content)
            return {
                'suspicious_urls': urls,
                'url_count': len(urls),
                'has_suspicious_urls': len(urls) > 0,
                'shortened_urls': sum(1 for url in urls if self._is_shortened_url(url))
            }
        except Exception as e:
            self.logger.error(f"URL feature extraction failed: {e}")
            return {'suspicious_urls': [], 'url_count': 0}
    
    def _extract_attachment_features(self, email_metadata: Dict) -> Dict[str, Any]:
        """提取附件特征"""
        attachments = email_metadata.get('attachments', [])
        
        suspicious_extensions = ['.exe', '.scr', '.bat', '.com', '.pif', '.vbs', '.js']
        suspicious_attachments = []
        
        for attachment in attachments:
            filename = attachment.get('filename', '')
            if any(filename.lower().endswith(ext) for ext in suspicious_extensions):
                suspicious_attachments.append(filename)
        
        return {
            'has_attachments': len(attachments) > 0,
            'attachment_count': len(attachments),
            'attachment_types': [att.get('type', '') for att in attachments],
            'suspicious_attachments': suspicious_attachments,
            'has_suspicious_attachments': len(suspicious_attachments) > 0
        }
    
    def _extract_metadata_features(self, email_metadata: Dict) -> Dict[str, Any]:
        """提取元数据特征"""
        sender = email_metadata.get('sender', '')
        
        return {
            'sender': sender,
            'sender_domain': sender.split('@')[1] if '@' in sender else '',
            'has_spf': email_metadata.get('spf_valid', False),
            'has_dkim': email_metadata.get('dkim_valid', False),
            'has_dmarc': email_metadata.get('dmarc_valid', False),
            'sender_reputation': self._calculate_sender_reputation(sender),
            'domain_age': email_metadata.get('domain_age', 0)
        }
    
    def _find_suspicious_words(self, text: str) -> List[str]:
        """查找可疑词汇"""
        suspicious_words = [
            'urgent', 'immediate', 'verify', 'suspend', 'click here',
            'act now', 'limited time', 'expire', 'confirm', 'update',
            'security alert', 'account locked', 'unauthorized access'
        ]
        
        text_lower = text.lower()
        found_words = [word for word in suspicious_words if word in text_lower]
        
        return found_words
    
    def _is_shortened_url(self, url: str) -> bool:
        """检查是否为短链接"""
        short_domains = ['bit.ly', 'tinyurl.com', 't.co', 'goo.gl', 'ow.ly']
        return any(domain in url for domain in short_domains)
    
    def _calculate_sender_reputation(self, sender: str) -> float:
        """计算发件人信誉度"""
        # 简化实现：基于域名判断
        if not sender or '@' not in sender:
            return 0.0
        
        domain = sender.split('@')[1].lower()
        
        # 知名域名高信誉度
        trusted_domains = ['gmail.com', 'outlook.com', 'yahoo.com', 'hotmail.com']
        if domain in trusted_domains:
            return 0.8
        
        # 企业域名中等信誉度
        if not any(domain.endswith(tld) for tld in ['.tk', '.ml', '.ga', '.cf']):
            return 0.6
        
        # 可疑域名低信誉度
        return 0.2
    
    def _determine_risk_level(self, confidence: float, threshold: float) -> str:
        """确定风险等级"""
        if confidence < threshold:
            return 'safe'
        elif confidence < threshold + 0.2:
            return 'low'
        elif confidence < threshold + 0.4:
            return 'medium'
        else:
            return 'high'

    def _calculate_feature_importance(self, email_content: str, email_metadata: Dict, confidence: float) -> Dict[str, float]:
        """
        计算特征重要性

        Args:
            email_content: 邮件内容
            email_metadata: 邮件元数据
            confidence: 检测置信度

        Returns:
            特征重要性字典
        """
        try:
            # 基于邮件内容和检测结果计算各特征的重要性
            text_importance = self._calculate_text_importance(email_content, confidence)
            url_importance = self._calculate_url_importance(email_content, confidence)
            metadata_importance = self._calculate_metadata_importance(email_metadata, confidence)
            attachment_importance = self._calculate_attachment_importance(email_metadata, confidence)

            # 归一化处理，确保总和为1
            total = text_importance + url_importance + metadata_importance + attachment_importance
            if total > 0:
                return {
                    'text': round(text_importance / total, 3),
                    'url': round(url_importance / total, 3),
                    'metadata': round(metadata_importance / total, 3),
                    'attachment': round(attachment_importance / total, 3)
                }
            else:
                # 默认均匀分布
                return {
                    'text': 0.25,
                    'url': 0.25,
                    'metadata': 0.25,
                    'attachment': 0.25
                }

        except Exception as e:
            self.logger.error(f"Feature importance calculation failed: {e}")
            # 返回默认值
            return {
                'text': 0.25,
                'url': 0.25,
                'metadata': 0.25,
                'attachment': 0.25
            }

    def _calculate_text_importance(self, email_content: str, confidence: float) -> float:
        """计算文本特征重要性"""
        importance = 0.0

        # 检查可疑词汇
        suspicious_words = self._find_suspicious_words(email_content)
        if suspicious_words:
            importance += len(suspicious_words) * 0.1

        # 检查紧急语言
        urgent_patterns = ['urgent', 'immediate', 'asap', '立即', '紧急', '马上']
        for pattern in urgent_patterns:
            if pattern.lower() in email_content.lower():
                importance += 0.15

        # 检查验证请求
        verify_patterns = ['verify', 'confirm', 'validate', '验证', '确认']
        for pattern in verify_patterns:
            if pattern.lower() in email_content.lower():
                importance += 0.1

        # 基于置信度调整
        if confidence > 0.7:
            importance *= 1.2
        elif confidence < 0.3:
            importance *= 0.8

        return min(importance, 1.0)

    def _extract_urls(self, text: str) -> List[str]:
        """从文本中提取URL"""
        import re

        # URL正则表达式模式
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        urls = re.findall(url_pattern, text)

        # 也检查www开头的URL
        www_pattern = r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        www_urls = re.findall(www_pattern, text)

        # 合并并去重
        all_urls = list(set(urls + ['http://' + url for url in www_urls]))

        return all_urls

    def _calculate_url_importance(self, email_content: str, confidence: float) -> float:
        """计算URL特征重要性"""
        importance = 0.0

        # 检查URL数量
        urls = self._extract_urls(email_content)
        if urls:
            importance += len(urls) * 0.1

            # 检查可疑URL特征
            for url in urls:
                if any(suspicious in url.lower() for suspicious in ['bit.ly', 'tinyurl', 'short']):
                    importance += 0.2
                if any(suspicious in url.lower() for suspicious in ['login', 'verify', 'secure']):
                    importance += 0.15

        # 基于置信度调整
        if confidence > 0.7:
            importance *= 1.1

        return min(importance, 1.0)

    def _calculate_metadata_importance(self, email_metadata: Dict, confidence: float) -> float:
        """计算元数据特征重要性"""
        importance = 0.0

        sender = email_metadata.get('sender', '')
        if sender:
            # 检查发件人域名
            domain = sender.split('@')[1] if '@' in sender else ''
            trusted_domains = ['gmail.com', 'outlook.com', 'yahoo.com']
            if domain not in trusted_domains:
                importance += 0.2

        # 检查SPF/DKIM/DMARC
        if not email_metadata.get('spf_valid', True):
            importance += 0.15
        if not email_metadata.get('dkim_valid', True):
            importance += 0.1
        if not email_metadata.get('dmarc_valid', True):
            importance += 0.1

        # 基于置信度调整
        if confidence > 0.6:
            importance *= 1.1

        return min(importance, 1.0)

    def _calculate_attachment_importance(self, email_metadata: Dict, confidence: float) -> float:
        """计算附件特征重要性"""
        importance = 0.0

        attachments = email_metadata.get('attachments', [])
        if attachments:
            importance += len(attachments) * 0.1

            # 检查可疑附件类型
            suspicious_types = ['.exe', '.scr', '.bat', '.com', '.pif', '.zip']
            for attachment in attachments:
                if any(attachment.lower().endswith(ext) for ext in suspicious_types):
                    importance += 0.2

        return min(importance, 1.0)

    def _fallback_detection(self, email_content: str, detection_mode: str) -> Dict[str, Any]:
        """使用简单回退检测器"""
        try:
            result = self.fallback_detector.detect(email_content)

            # 转换为标准格式
            return {
                'is_phishing': result['is_phishing'],
                'confidence': result['confidence'],
                'risk_level': self._determine_risk_level(result['confidence'], 0.3),
                'text_features': {
                    'suspicious_words': result['details']['found_keywords'],
                    'suspicious_word_count': result['details']['keyword_score'],
                    'text_length': len(email_content),
                    'word_count': len(email_content.split())
                },
                'url_features': {
                    'suspicious_urls': [],
                    'url_count': 0,
                    'shortened_urls': 0
                },
                'attachment_features': {
                    'has_attachments': False,
                    'attachment_types': [],
                    'suspicious_attachments': []
                },
                'metadata_features': {
                    'sender_reputation': 0.5,
                    'domain_age': 365,
                    'spf_valid': True
                },
                'prediction_details': {
                    'detection_mode': detection_mode,
                    'model_type': 'rule_based_fallback',
                    'found_keywords': result['details']['found_keywords'],
                    'found_domains': result['details']['found_domains'],
                    'risk_score': result['risk_score'],
                    'model_version': 'fallback_v1.0'
                }
            }
        except Exception as e:
            self.logger.error(f"Fallback detection failed: {e}")
            return self._mock_detection(email_content, detection_mode)

    def _mock_detection(self, email_content: str, detection_mode: str) -> Dict[str, Any]:
        """模拟检测（当AI模型不可用时）"""
        import random
        
        # 基于内容的简单启发式检测
        suspicious_indicators = 0
        
        # 检查可疑词汇
        suspicious_words = self._find_suspicious_words(email_content)
        suspicious_indicators += len(suspicious_words)
        
        # 检查URL
        import re
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', email_content)
        suspicious_indicators += len(urls)
        
        # 计算基础置信度
        base_confidence = min(suspicious_indicators * 0.2, 0.9)
        
        # 零样本模式增加一些随机性（模拟对未知攻击的不确定性）
        if detection_mode == 'zero_shot':
            confidence = base_confidence + random.uniform(-0.1, 0.1)
        else:
            confidence = base_confidence + random.uniform(-0.05, 0.05)
        
        confidence = max(0.1, min(0.95, confidence))
        is_phishing = confidence > 0.5

        # 计算特征重要性
        feature_importance = self._calculate_feature_importance(
            email_content, {}, confidence
        )

        return {
            'is_phishing': is_phishing,
            'confidence': confidence,
            'risk_level': self._determine_risk_level(confidence, 0.5),
            'text_features': {
                'suspicious_words': suspicious_words,
                'suspicious_word_count': len(suspicious_words),
                'text_length': len(email_content),
                'word_count': len(email_content.split())
            },
            'url_features': {
                'suspicious_urls': urls,
                'url_count': len(urls),
                'shortened_urls': 0
            },
            'attachment_features': {
                'has_attachments': False,
                'attachment_types': [],
                'suspicious_attachments': []
            },
            'metadata_features': {
                'sender_reputation': random.uniform(0.3, 0.8),
                'domain_age': random.randint(30, 3650),
                'spf_valid': random.choice([True, False])
            },
            'prediction_details': {
                'detection_mode': detection_mode,
                'model_type': 'mock_heuristic',
                'suspicious_indicators': suspicious_indicators,
                'feature_importance': feature_importance,
                'model_version': 'v2.0'
            }
        }
    
    def train_model(self, training_data: List[Dict]) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            training_data: 训练数据
            
        Returns:
            训练结果
        """
        if not AI_MODELS_AVAILABLE or not self.detector:
            return {'status': 'error', 'message': 'AI models not available'}
        
        try:
            self.logger.info("Starting model training...")
            
            # 训练元学习模型
            training_history = self.detector.train_meta_learning(
                training_data, num_epochs=50
            )
            
            # 保存模型
            model_path = project_root / 'models' / 'zero_shot_detector'
            self.detector.save_model(str(model_path))
            
            return {
                'status': 'success',
                'training_history': training_history,
                'model_path': str(model_path)
            }
            
        except Exception as e:
            self.logger.error(f"Model training failed: {e}")
            return {'status': 'error', 'message': str(e)}


# 全局模型管理器实例
ai_manager = AIModelManager()
