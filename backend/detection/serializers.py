from rest_framework import serializers
from .models import EmailFile, DetectionResult, SystemConfig, BatchDetectionJob


class EmailFileSerializer(serializers.ModelSerializer):
    """邮件文件序列化器"""
    
    class Meta:
        model = EmailFile
        fields = ['id', 'filename', 'file_path', 'file_size', 'upload_time', 'user']
        read_only_fields = ['id', 'file_size', 'upload_time', 'user']
    
    def validate_file_path(self, value):
        """验证文件类型"""
        allowed_extensions = ['.eml', '.msg', '.txt', '.mbox']
        file_extension = value.name.lower().split('.')[-1]
        
        if f'.{file_extension}' not in allowed_extensions:
            raise serializers.ValidationError(
                f"不支持的文件类型。支持的类型: {', '.join(allowed_extensions)}"
            )
        
        # 文件大小限制 (50MB)
        if value.size > 50 * 1024 * 1024:
            raise serializers.ValidationError("文件大小不能超过50MB")
        
        return value


class DetectionResultSerializer(serializers.ModelSerializer):
    """检测结果序列化器"""
    email_file = EmailFileSerializer(read_only=True)
    risk_level_display = serializers.CharField(source='get_risk_level_display', read_only=True)
    
    class Meta:
        model = DetectionResult
        fields = [
            'id', 'email_file', 'is_phishing', 'confidence', 'risk_level', 
            'risk_level_display', 'detection_time', 'processing_time',
            'text_features', 'url_features', 'attachment_features', 
            'metadata_features', 'model_version', 'prediction_details'
        ]
        read_only_fields = ['id', 'detection_time']


class DetectionRequestSerializer(serializers.Serializer):
    """检测请求序列化器"""
    file = serializers.FileField()
    detection_mode = serializers.ChoiceField(
        choices=[('standard', '标准检测'), ('zero_shot', '零样本检测')],
        default='standard'
    )
    
    def validate_file(self, value):
        """验证上传文件"""
        allowed_extensions = ['.eml', '.msg', '.txt', '.mbox']
        file_extension = value.name.lower().split('.')[-1]
        
        if f'.{file_extension}' not in allowed_extensions:
            raise serializers.ValidationError(
                f"不支持的文件类型。支持的类型: {', '.join(allowed_extensions)}"
            )
        
        if value.size > 50 * 1024 * 1024:
            raise serializers.ValidationError("文件大小不能超过50MB")
        
        return value


class SystemConfigSerializer(serializers.ModelSerializer):
    """系统配置序列化器"""
    
    class Meta:
        model = SystemConfig
        fields = ['key', 'value', 'description', 'updated_time']
        read_only_fields = ['updated_time']


class DetectionHistorySerializer(serializers.ModelSerializer):
    """检测历史序列化器（简化版）"""
    filename = serializers.CharField(source='email_file.filename', read_only=True)
    upload_time = serializers.DateTimeField(source='email_file.upload_time', read_only=True)
    risk_level_display = serializers.CharField(source='get_risk_level_display', read_only=True)
    
    class Meta:
        model = DetectionResult
        fields = [
            'id', 'filename', 'upload_time', 'is_phishing', 
            'confidence', 'risk_level', 'risk_level_display', 
            'detection_time', 'processing_time'
        ]


class StatisticsSerializer(serializers.Serializer):
    """统计数据序列化器"""
    total_detections = serializers.IntegerField()
    phishing_count = serializers.IntegerField()
    safe_count = serializers.IntegerField()
    phishing_rate = serializers.FloatField()
    avg_confidence = serializers.FloatField()
    avg_processing_time = serializers.FloatField()
    risk_level_distribution = serializers.DictField()
    daily_detections = serializers.ListField()


class BatchDetectionJobSerializer(serializers.ModelSerializer):
    """批量检测任务序列化器"""
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    detection_mode_display = serializers.CharField(source='get_detection_mode_display', read_only=True)
    progress_percentage = serializers.ReadOnlyField()
    phishing_percentage = serializers.ReadOnlyField()

    # 时间字段格式化
    created_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    start_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    end_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = BatchDetectionJob
        fields = [
            'id', 'name', 'description', 'status', 'status_display',
            'detection_mode', 'detection_mode_display', 'created_time',
            'start_time', 'end_time', 'total_files', 'processed_files',
            'phishing_count', 'safe_count', 'avg_confidence',
            'high_risk_count', 'medium_risk_count', 'low_risk_count', 'safe_risk_count',
            'progress_percentage', 'phishing_percentage', 'error_message'
        ]


class BatchDetectionJobDetailSerializer(BatchDetectionJobSerializer):
    """批量检测任务详情序列化器（包含检测结果）"""
    detection_results = DetectionResultSerializer(many=True, read_only=True)

    class Meta(BatchDetectionJobSerializer.Meta):
        fields = BatchDetectionJobSerializer.Meta.fields + ['detection_results']


class DetectionHistoryMixedSerializer(serializers.Serializer):
    """混合检测历史序列化器（单个检测 + 批量检测）"""
    type = serializers.CharField()  # 'single' 或 'batch'
    data = serializers.JSONField()  # 具体的数据内容
