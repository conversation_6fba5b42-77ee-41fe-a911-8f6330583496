from django.urls import path
from . import views

app_name = 'detection'

urlpatterns = [
    # 文件上传
    path('upload/', views.upload_email, name='upload_email'),
    
    # 检测接口
    path('detect/', views.detect_phishing, name='detect_phishing'),
    path('result/<uuid:result_id>/', views.get_detection_result, name='get_detection_result'),
    
    # 历史记录
    path('history/', views.get_detection_history, name='get_detection_history'),
    
    # 统计数据
    path('statistics/', views.get_statistics, name='get_statistics'),
    
    # 系统配置
    path('config/', views.system_config, name='system_config'),

    # 批量检测
    path('batch/upload/', views.batch_upload, name='batch_upload'),
    path('batch/detect/', views.batch_detect, name='batch_detect'),
    path('batch/status/<str:batch_id>/', views.batch_status, name='batch_status'),
    path('batch/results/<str:batch_id>/', views.batch_results, name='batch_results'),
    path('batch/detail/<uuid:batch_id>/', views.get_batch_detection_detail, name='get_batch_detection_detail'),
    path('batch/export/<str:batch_id>/', views.export_batch_results, name='export_batch_results'),
]
