#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/arctan_ops.h>

namespace at {


// aten::arctan(Tensor self) -> Tensor
inline at::Tensor arctan(const at::Tensor & self) {
    return at::_ops::arctan::call(self);
}

// aten::arctan_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & arctan_(at::Tensor & self) {
    return at::_ops::arctan_::call(self);
}

// aten::arctan.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & arctan_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::arctan_out::call(self, out);
}
// aten::arctan.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & arctan_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::arctan_out::call(self, out);
}

}
