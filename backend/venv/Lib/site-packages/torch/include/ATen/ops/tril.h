#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/tril_ops.h>

namespace at {


// aten::tril.out(Tensor self, int diagonal=0, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & tril_out(at::Tensor & out, const at::Tensor & self, int64_t diagonal=0) {
    return at::_ops::tril_out::call(self, diagonal, out);
}
// aten::tril.out(Tensor self, int diagonal=0, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & tril_outf(const at::Tensor & self, int64_t diagonal, at::Tensor & out) {
    return at::_ops::tril_out::call(self, diagonal, out);
}

// aten::tril(Tensor self, int diagonal=0) -> Tensor
inline at::Tensor tril(const at::Tensor & self, int64_t diagonal=0) {
    return at::_ops::tril::call(self, diagonal);
}

}
