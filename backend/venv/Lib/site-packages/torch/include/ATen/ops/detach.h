#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/detach_ops.h>

namespace at {


// aten::detach(Tensor(a) self) -> Tensor(a)
inline at::Tensor detach(const at::Tensor & self) {
    return at::_ops::detach::call(self);
}

// aten::detach_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & detach_(at::Tensor & self) {
    return at::_ops::detach_::call(self);
}

}
