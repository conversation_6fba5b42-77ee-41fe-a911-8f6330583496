# 钓鱼邮件检测系统测试指南

## 快速测试步骤

### 1. 启动系统
```bash
# 启动后端
cd backend
python manage.py runserver

# 启动前端
cd frontend
npm run dev
```

### 2. 访问系统
打开浏览器访问：http://localhost:5173

### 3. 测试邮件文件
我已经为您准备了6个测试邮件文件在 `测试邮件样本/` 目录中：

#### 正常邮件（预期结果：安全）
- `正常邮件1.txt` - 中文工作邮件
- `正常邮件2.txt` - 英文系统通知

#### 传统钓鱼邮件（预期结果：钓鱼邮件）
- `钓鱼邮件1.txt` - 中文银行钓鱼
- `钓鱼邮件2.txt` - 英文PayPal钓鱼

#### 零样本测试邮件（预期结果：钓鱼邮件，测试创新点二）
- `零样本测试邮件1.txt` - AI助手钓鱼（新兴攻击类型）
- `零样本测试邮件2.txt` - NFT钓鱼（新兴攻击类型）

### 4. 测试流程

#### 标准检测测试
1. 在前端选择"标准检测"模式
2. 上传测试邮件文件
3. 点击"开始检测"
4. 查看检测结果

#### 零样本检测测试（验证创新点二）
1. 在前端选择"零样本检测"模式
2. 上传零样本测试邮件
3. 点击"开始检测"
4. 观察系统是否能检测出未知类型的钓鱼攻击

### 5. 预期测试结果

| 邮件文件 | 检测模式 | 预期结果 | 验证点 |
|---------|---------|---------|--------|
| 正常邮件1.txt | 标准检测 | 安全 | 基础功能 |
| 正常邮件2.txt | 标准检测 | 安全 | 基础功能 |
| 钓鱼邮件1.txt | 标准检测 | 钓鱼邮件 | 基础功能 |
| 钓鱼邮件2.txt | 标准检测 | 钓鱼邮件 | 基础功能 |
| 零样本测试邮件1.txt | 零样本检测 | 钓鱼邮件 | 创新点二 |
| 零样本测试邮件2.txt | 零样本检测 | 钓鱼邮件 | 创新点二 |

### 6. 功能验证清单

#### ✅ 基础功能
- [ ] 文件上传功能正常
- [ ] 检测模式切换正常
- [ ] 检测结果显示正常
- [ ] 统计信息更新正常

#### ✅ 创新点一：多模态特征融合
- [ ] 系统能识别邮件中的URL
- [ ] 系统能分析发件人信息
- [ ] 系统能处理中英文邮件
- [ ] 检测结果包含多种特征分析

#### ✅ 创新点二：零样本学习
- [ ] 零样本模式能检测AI助手钓鱼
- [ ] 零样本模式能检测NFT钓鱼
- [ ] 动态阈值调整功能
- [ ] 对未知攻击类型的适应能力

### 7. 性能测试
- 检测速度：每个邮件应在3秒内完成检测
- 准确率：传统钓鱼邮件检测准确率应≥90%
- 零样本性能：未知类型钓鱼邮件检测准确率应≥80%

### 8. 问题排查

#### 如果检测结果不准确：
1. 检查AI模型是否正确加载
2. 查看后端日志是否有错误
3. 确认是否使用了正确的检测模式

#### 如果系统报错：
1. 检查文件格式是否正确（.txt文件）
2. 确认文件大小不超过限制
3. 查看浏览器控制台错误信息

### 9. 演示建议

#### 对导师/评委演示时：
1. **先演示基础功能**：上传正常邮件和传统钓鱼邮件，展示基本检测能力
2. **重点演示创新点**：
   - 上传零样本测试邮件，强调这是"未知类型"的攻击
   - 解释系统如何通过对抗学习和元学习检测未知攻击
   - 展示动态阈值调整功能
3. **展示技术细节**：
   - 多模态特征融合（URL、发件人、内容分析）
   - 中英文支持
   - 实时检测能力

#### 技术亮点说明：
- "这个AI助手钓鱼邮件是全新的攻击类型，传统方法无法检测"
- "我们的零样本学习机制能够识别未见过的攻击模式"
- "系统会根据邮件图结构动态调整检测阈值"
- "多模态特征融合提供了更全面的威胁分析"

### 10. 扩展测试

如果需要更多测试数据，可以：
1. 修改现有邮件内容创建变种
2. 从网上搜索真实的钓鱼邮件案例（注意安全）
3. 创建更多新兴攻击类型的模拟邮件

这样您就可以快速验证系统的所有功能，特别是两个创新点的实现效果！
