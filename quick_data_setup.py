#!/usr/bin/env python3
"""
快速数据生成脚本
一键生成不同规模的测试数据集

使用方法：
python quick_data_setup.py --preset demo     # 演示用数据集（10K）
python quick_data_setup.py --preset test     # 测试用数据集（50K）
python quick_data_setup.py --preset stress   # 压力测试数据集（100K）
"""

import os
import sys
import time
import argparse
from pathlib import Path

def run_command(command):
    """执行命令并显示进度"""
    print(f"🔄 执行: {command}")
    start_time = time.time()
    result = os.system(command)
    end_time = time.time()
    
    if result == 0:
        print(f"✅ 完成 (耗时: {end_time - start_time:.1f}秒)")
        return True
    else:
        print(f"❌ 失败")
        return False

def setup_demo_data():
    """设置演示数据（10K邮件）"""
    print("🎯 生成演示数据集...")
    print("=" * 50)
    
    commands = [
        "python enhanced_email_generator.py --count 10000 --output demo_10k_emails.zip --normal-ratio 0.6",
        "python generate_test_emails.py --count 5000 --output backup_5k_emails.zip"
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            return False
    
    print("\n📊 演示数据集生成完成！")
    print("📁 文件列表：")
    print("   - demo_10k_emails.zip (主要数据集)")
    print("   - demo_10k_emails_labels.csv")
    print("   - backup_5k_emails.zip (备用数据集)")
    print("   - backup_5k_emails_labels.txt")
    
    return True

def setup_test_data():
    """设置测试数据（50K邮件）"""
    print("🧪 生成测试数据集...")
    print("=" * 50)
    
    commands = [
        "python enhanced_email_generator.py --count 50000 --output test_50k_emails.zip --normal-ratio 0.65 --quality-check",
        "python enhanced_email_generator.py --count 20000 --output zero_shot_20k.zip --normal-ratio 0.4"
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            return False
    
    print("\n📊 测试数据集生成完成！")
    print("📁 文件列表：")
    print("   - test_50k_emails.zip (标准测试集)")
    print("   - zero_shot_20k.zip (零样本测试集)")
    print("   - 对应的标签和统计文件")
    
    return True

def setup_stress_data():
    """设置压力测试数据（100K+邮件）"""
    print("💪 生成压力测试数据集...")
    print("=" * 50)
    
    commands = [
        "python enhanced_email_generator.py --count 100000 --output stress_100k_emails.zip --normal-ratio 0.6 --variants 5 --quality-check",
        "python enhanced_email_generator.py --count 50000 --output adversarial_50k.zip --normal-ratio 0.3"
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            return False
    
    print("\n📊 压力测试数据集生成完成！")
    print("📁 文件列表：")
    print("   - stress_100k_emails.zip (大规模数据集)")
    print("   - adversarial_50k.zip (对抗样本集)")
    print("   - 对应的标签和统计文件")
    
    return True

def setup_custom_data(count, normal_ratio, output_name):
    """设置自定义数据"""
    print(f"🎨 生成自定义数据集 ({count:,} 邮件)...")
    print("=" * 50)
    
    cmd = f"python enhanced_email_generator.py --count {count} --output {output_name} --normal-ratio {normal_ratio} --quality-check"
    
    if run_command(cmd):
        print(f"\n📊 自定义数据集生成完成！")
        print(f"📁 输出文件: {output_name}")
        return True
    return False

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_files = [
        "enhanced_email_generator.py",
        "generate_test_emails.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 依赖检查通过")
    return True

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 60)
    print("📋 使用指南")
    print("=" * 60)
    print("1. 启动后端服务:")
    print("   cd backend")
    print("   python manage.py runserver")
    print()
    print("2. 启动前端服务:")
    print("   cd frontend")
    print("   npm run dev")
    print()
    print("3. 访问批量检测页面:")
    print("   http://localhost:5173/batch-detection")
    print()
    print("4. 上传生成的ZIP文件进行测试")
    print("=" * 60)

def main():
    parser = argparse.ArgumentParser(description='快速数据生成脚本')
    parser.add_argument('--preset', choices=['demo', 'test', 'stress'], 
                       help='预设数据集类型')
    parser.add_argument('--count', type=int, 
                       help='自定义邮件数量')
    parser.add_argument('--normal-ratio', type=float, default=0.6,
                       help='正常邮件比例 (默认: 0.6)')
    parser.add_argument('--output', type=str,
                       help='自定义输出文件名')
    parser.add_argument('--skip-check', action='store_true',
                       help='跳过依赖检查')
    
    args = parser.parse_args()
    
    print("🚀 快速数据生成脚本")
    print("=" * 50)
    
    # 检查依赖
    if not args.skip_check and not check_dependencies():
        sys.exit(1)
    
    success = False
    
    if args.preset == 'demo':
        success = setup_demo_data()
    elif args.preset == 'test':
        success = setup_test_data()
    elif args.preset == 'stress':
        success = setup_stress_data()
    elif args.count and args.output:
        success = setup_custom_data(args.count, args.normal_ratio, args.output)
    else:
        print("❌ 请指定 --preset 或 --count + --output")
        parser.print_help()
        sys.exit(1)
    
    if success:
        print("\n🎉 数据生成成功！")
        show_usage_guide()
    else:
        print("\n💥 数据生成失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
