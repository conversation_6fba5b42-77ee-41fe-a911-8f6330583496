"""
邮件数据收集器
用于收集、生成和处理钓鱼邮件检测系统的测试数据

功能：
1. 下载公开数据集
2. 生成合成邮件数据
3. 数据清洗和标准化
4. 创建训练/验证/测试集划分
"""

import os
import json
import csv
import random
import requests
import tarfile
import zipfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EmailDataCollector:
    """邮件数据收集器"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.raw_dir = self.data_dir / "raw"
        self.processed_dir = self.data_dir / "processed"
        self.generated_dir = self.data_dir / "generated"
        self.splits_dir = self.data_dir / "splits"
        
        for dir_path in [self.raw_dir, self.processed_dir, self.generated_dir, self.splits_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def download_public_datasets(self):
        """下载公开数据集"""
        logger.info("开始下载公开数据集...")
        
        # 1. 下载SpamAssassin数据集
        self._download_spamassassin()
        
        # 2. 下载示例钓鱼邮件数据
        self._download_sample_phishing_data()
        
        logger.info("公开数据集下载完成")
    
    def _download_spamassassin(self):
        """下载SpamAssassin数据集"""
        logger.info("下载SpamAssassin数据集...")
        
        spam_dir = self.raw_dir / "spamassassin"
        spam_dir.mkdir(exist_ok=True)
        
        # SpamAssassin公开语料库URL
        datasets = [
            ("20021010_easy_ham.tar.bz2", "https://spamassassin.apache.org/old/publiccorpus/20021010_easy_ham.tar.bz2"),
            ("20021010_hard_ham.tar.bz2", "https://spamassassin.apache.org/old/publiccorpus/20021010_hard_ham.tar.bz2"),
            ("20021010_spam.tar.bz2", "https://spamassassin.apache.org/old/publiccorpus/20021010_spam.tar.bz2"),
        ]
        
        for filename, url in datasets:
            file_path = spam_dir / filename
            if not file_path.exists():
                try:
                    logger.info(f"下载 {filename}...")
                    response = requests.get(url, stream=True, timeout=30)
                    response.raise_for_status()
                    
                    with open(file_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    logger.info(f"{filename} 下载完成")
                except Exception as e:
                    logger.error(f"下载 {filename} 失败: {e}")
            else:
                logger.info(f"{filename} 已存在，跳过下载")
    
    def _download_sample_phishing_data(self):
        """下载示例钓鱼邮件数据"""
        logger.info("创建示例钓鱼邮件数据...")
        
        # 由于真实钓鱼邮件数据可能包含恶意内容，这里创建示例数据
        phishing_dir = self.raw_dir / "sample_phishing"
        phishing_dir.mkdir(exist_ok=True)
        
        # 创建示例钓鱼邮件
        sample_phishing_emails = self._generate_sample_phishing_emails()
        
        for i, email in enumerate(sample_phishing_emails):
            file_path = phishing_dir / f"phishing_{i+1:04d}.txt"
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(email)
        
        logger.info(f"创建了 {len(sample_phishing_emails)} 个示例钓鱼邮件")
    
    def _generate_sample_phishing_emails(self) -> List[str]:
        """生成示例钓鱼邮件"""
        templates = [
            # 银行钓鱼模板
            """Subject: Urgent: Your Bank Account Security Alert
From: security@{bank_domain}
To: <EMAIL>
Date: {date}

Dear Valued Customer,

We have detected suspicious activity on your account. Your account will be suspended within 24 hours unless you verify your information immediately.

Click here to verify: http://{phishing_domain}/verify

Please provide:
- Account number
- PIN
- Social Security Number

Failure to respond will result in permanent account closure.

Best regards,
Security Team
{bank_name}
""",
            
            # 科技公司钓鱼模板
            """Subject: Action Required: Verify Your {company} Account
From: noreply@{company_domain}
To: <EMAIL>
Date: {date}

Hello,

Your {company} account has been temporarily limited due to unusual activity. To restore full access, please verify your account information.

Verify Now: http://{phishing_domain}/account-verify

This link will expire in 2 hours. If you don't verify, your account will be permanently suspended.

Thank you,
{company} Security Team
""",
            
            # 购物网站钓鱼模板
            """Subject: Your Order #{order_id} - Payment Failed
From: orders@{shopping_domain}
To: <EMAIL>
Date: {date}

Dear Customer,

Your recent order could not be processed due to payment failure. Please update your payment information to avoid order cancellation.

Order Details:
- Order #: {order_id}
- Amount: ${amount}
- Items: {items}

Update Payment: http://{phishing_domain}/payment-update

If not updated within 24 hours, your order will be cancelled.

Best regards,
Customer Service Team
""",
        ]
        
        # 生成变量值
        bank_domains = ["bank-security.com", "secure-banking.net", "bankverify.org"]
        bank_names = ["First National Bank", "Security Bank", "Trust Bank"]
        company_domains = ["account-verify.com", "security-check.net", "user-verify.org"]
        companies = ["Microsoft", "Google", "Apple", "Amazon"]
        shopping_domains = ["order-update.com", "payment-fix.net", "shop-verify.org"]
        phishing_domains = ["secure-verify.com", "account-check.net", "user-confirm.org"]
        
        emails = []
        for _ in range(50):  # 生成50个示例邮件
            template = random.choice(templates)
            
            # 生成随机日期（最近30天内）
            days_ago = random.randint(0, 30)
            date = (datetime.now() - timedelta(days=days_ago)).strftime("%a, %d %b %Y %H:%M:%S +0000")
            
            # 填充模板变量
            email = template.format(
                bank_domain=random.choice(bank_domains),
                bank_name=random.choice(bank_names),
                company_domain=random.choice(company_domains),
                company=random.choice(companies),
                shopping_domain=random.choice(shopping_domains),
                phishing_domain=random.choice(phishing_domains),
                date=date,
                order_id=f"ORD{random.randint(100000, 999999)}",
                amount=f"{random.randint(50, 500)}.{random.randint(10, 99)}",
                items=random.choice(["Electronics", "Clothing", "Books", "Home & Garden"])
            )
            
            emails.append(email)
        
        return emails
    
    def generate_normal_emails(self, count: int = 1000) -> List[str]:
        """生成正常邮件"""
        logger.info(f"生成 {count} 个正常邮件...")
        
        templates = [
            # 工作邮件模板
            """Subject: Meeting Reminder - {meeting_topic}
From: {sender}@{company_domain}
To: team@{company_domain}
Date: {date}

Hi Team,

This is a reminder about our meeting scheduled for {meeting_time}.

Agenda:
- {agenda_item1}
- {agenda_item2}
- {agenda_item3}

Please come prepared with your updates.

Best regards,
{sender_name}
""",
            
            # 通知邮件模板
            """Subject: {notification_type} - {subject_detail}
From: notifications@{service_domain}
To: <EMAIL>
Date: {date}

Hello,

This is to inform you about {notification_content}.

Details:
- Date: {event_date}
- Time: {event_time}
- Location: {location}

If you have any questions, please contact our support team.

Best regards,
{service_name} Team
""",
            
            # 个人邮件模板
            """Subject: {personal_subject}
From: {friend_name}@{email_provider}
To: <EMAIL>
Date: {date}

Hi there!

{personal_message}

Hope to hear from you soon!

Best,
{friend_name}
""",
        ]
        
        # 生成变量值
        company_domains = ["company.com", "corp.net", "business.org", "enterprise.com"]
        service_domains = ["service.com", "platform.net", "system.org", "app.com"]
        email_providers = ["gmail.com", "outlook.com", "yahoo.com", "hotmail.com"]
        
        meeting_topics = ["Project Review", "Budget Planning", "Team Building", "Product Launch"]
        notification_types = ["System Update", "Service Maintenance", "Account Update", "Newsletter"]
        personal_subjects = ["Weekend Plans", "Birthday Party", "Vacation Photos", "Book Recommendation"]
        
        emails = []
        for _ in range(count):
            template = random.choice(templates)
            
            # 生成随机日期
            days_ago = random.randint(0, 90)
            date = (datetime.now() - timedelta(days=days_ago)).strftime("%a, %d %b %Y %H:%M:%S +0000")
            
            # 填充模板变量
            email = template.format(
                meeting_topic=random.choice(meeting_topics),
                sender=f"user{random.randint(1, 100)}",
                company_domain=random.choice(company_domains),
                date=date,
                meeting_time=f"{random.randint(9, 17)}:00",
                agenda_item1="Status updates",
                agenda_item2="Next steps",
                agenda_item3="Q&A session",
                sender_name=f"User {random.randint(1, 100)}",
                notification_type=random.choice(notification_types),
                subject_detail="Important Information",
                service_domain=random.choice(service_domains),
                notification_content="an important update to our service",
                event_date=date.split()[1:4],
                event_time=f"{random.randint(9, 17)}:00",
                location="Conference Room A",
                service_name="Service",
                personal_subject=random.choice(personal_subjects),
                friend_name=f"Friend{random.randint(1, 50)}",
                email_provider=random.choice(email_providers),
                personal_message="Hope you're doing well! Just wanted to catch up and see how things are going."
            )
            
            emails.append(email)
        
        return emails
    
    def create_dataset_splits(self, train_ratio: float = 0.7, val_ratio: float = 0.15, test_ratio: float = 0.15):
        """创建数据集划分"""
        logger.info("创建数据集划分...")
        
        # 确保比例和为1
        assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "比例和必须为1"
        
        # 收集所有处理后的数据
        all_emails = []
        
        # 读取正常邮件
        normal_emails = self.generate_normal_emails(6000)  # 生成6000个正常邮件
        for email in normal_emails:
            all_emails.append({
                'content': email,
                'label': 0,  # 0表示正常邮件
                'type': 'normal'
            })
        
        # 读取钓鱼邮件
        phishing_dir = self.raw_dir / "sample_phishing"
        if phishing_dir.exists():
            for file_path in phishing_dir.glob("*.txt"):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                all_emails.append({
                    'content': content,
                    'label': 1,  # 1表示钓鱼邮件
                    'type': 'phishing'
                })
        
        # 随机打乱数据
        random.shuffle(all_emails)
        
        # 计算划分点
        total_count = len(all_emails)
        train_count = int(total_count * train_ratio)
        val_count = int(total_count * val_ratio)
        
        # 划分数据
        train_data = all_emails[:train_count]
        val_data = all_emails[train_count:train_count + val_count]
        test_data = all_emails[train_count + val_count:]
        
        # 保存划分结果
        self._save_split_data(train_data, "train")
        self._save_split_data(val_data, "validation")
        self._save_split_data(test_data, "test")
        
        logger.info(f"数据集划分完成:")
        logger.info(f"  训练集: {len(train_data)} 样本")
        logger.info(f"  验证集: {len(val_data)} 样本")
        logger.info(f"  测试集: {len(test_data)} 样本")
        logger.info(f"  总计: {total_count} 样本")
    
    def _save_split_data(self, data: List[Dict], split_name: str):
        """保存数据划分"""
        split_dir = self.splits_dir / split_name
        split_dir.mkdir(exist_ok=True)
        
        # 保存为JSON格式
        json_path = split_dir / f"{split_name}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 保存为CSV格式
        csv_path = split_dir / f"{split_name}.csv"
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['content', 'label', 'type'])
            writer.writeheader()
            writer.writerows(data)
        
        logger.info(f"保存 {split_name} 数据到 {split_dir}")
    
    def get_data_statistics(self) -> Dict:
        """获取数据统计信息"""
        stats = {
            'total_emails': 0,
            'normal_emails': 0,
            'phishing_emails': 0,
            'splits': {}
        }
        
        for split_name in ['train', 'validation', 'test']:
            json_path = self.splits_dir / split_name / f"{split_name}.json"
            if json_path.exists():
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                normal_count = sum(1 for item in data if item['label'] == 0)
                phishing_count = sum(1 for item in data if item['label'] == 1)
                
                stats['splits'][split_name] = {
                    'total': len(data),
                    'normal': normal_count,
                    'phishing': phishing_count
                }
                
                stats['total_emails'] += len(data)
                stats['normal_emails'] += normal_count
                stats['phishing_emails'] += phishing_count
        
        return stats


def main():
    """主函数"""
    print("🚀 邮件数据收集器启动")
    print("=" * 50)
    
    # 创建数据收集器
    collector = EmailDataCollector()
    
    # 1. 下载公开数据集
    print("\n📥 步骤1: 下载公开数据集")
    collector.download_public_datasets()
    
    # 2. 创建数据集划分
    print("\n📊 步骤2: 创建数据集划分")
    collector.create_dataset_splits()
    
    # 3. 显示统计信息
    print("\n📈 步骤3: 数据统计")
    stats = collector.get_data_statistics()
    
    print(f"总邮件数: {stats['total_emails']}")
    print(f"正常邮件: {stats['normal_emails']} ({stats['normal_emails']/stats['total_emails']*100:.1f}%)")
    print(f"钓鱼邮件: {stats['phishing_emails']} ({stats['phishing_emails']/stats['total_emails']*100:.1f}%)")
    
    print("\n各数据集分布:")
    for split_name, split_stats in stats['splits'].items():
        print(f"  {split_name}: {split_stats['total']} 样本 "
              f"(正常: {split_stats['normal']}, 钓鱼: {split_stats['phishing']})")
    
    print("\n✅ 数据收集完成!")
    print(f"数据保存在: {collector.data_dir}")


if __name__ == "__main__":
    main()
