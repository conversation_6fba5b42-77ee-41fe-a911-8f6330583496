"""
数据集管理工具
整合和管理钓鱼邮件检测系统的所有测试数据

功能：
1. 整合多个数据源
2. 数据质量检查和清洗
3. 创建标准化数据集
4. 生成数据集报告
5. 导出不同格式的数据
"""

import json
import csv
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import logging
from collections import Counter
import hashlib
import re

logger = logging.getLogger(__name__)


class DatasetManager:
    """数据集管理器"""
    
    def __init__(self, data_root: str = "data"):
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "final_dataset"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据源目录
        self.raw_dir = self.data_root / "raw"
        self.processed_dir = self.data_root / "processed"
        self.generated_dir = self.data_root / "generated"
        self.splits_dir = self.data_root / "splits"
        
        # 数据统计
        self.dataset_stats = {
            'total_emails': 0,
            'normal_emails': 0,
            'phishing_emails': 0,
            'languages': Counter(),
            'sources': Counter(),
            'quality_issues': []
        }
    
    def collect_all_data(self) -> List[Dict]:
        """收集所有数据源的数据"""
        logger.info("开始收集所有数据源...")
        
        all_data = []
        
        # 1. 收集基础数据集
        basic_data = self._load_basic_datasets()
        all_data.extend(basic_data)
        logger.info(f"收集基础数据: {len(basic_data)} 条")
        
        # 2. 收集生成的数据
        generated_data = self._load_generated_datasets()
        all_data.extend(generated_data)
        logger.info(f"收集生成数据: {len(generated_data)} 条")
        
        # 3. 收集分割数据
        split_data = self._load_split_datasets()
        all_data.extend(split_data)
        logger.info(f"收集分割数据: {len(split_data)} 条")
        
        logger.info(f"总计收集数据: {len(all_data)} 条")
        return all_data
    
    def _load_basic_datasets(self) -> List[Dict]:
        """加载基础数据集"""
        data = []
        
        # 从splits目录加载
        for split_name in ['train', 'validation', 'test']:
            json_file = self.splits_dir / split_name / f"{split_name}.json"
            if json_file.exists():
                with open(json_file, 'r', encoding='utf-8') as f:
                    split_data = json.load(f)
                    for item in split_data:
                        item['source'] = f'basic_{split_name}'
                        item['split'] = split_name
                    data.extend(split_data)
        
        return data
    
    def _load_generated_datasets(self) -> List[Dict]:
        """加载生成的数据集"""
        data = []
        
        if not self.generated_dir.exists():
            return data
        
        # 加载所有生成的JSON文件
        for json_file in self.generated_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                    for item in file_data:
                        item['source'] = f'generated_{json_file.stem}'
                        item['split'] = 'generated'
                    data.extend(file_data)
            except Exception as e:
                logger.error(f"加载 {json_file} 失败: {e}")
        
        return data
    
    def _load_split_datasets(self) -> List[Dict]:
        """加载已分割的数据集"""
        # 这个方法在_load_basic_datasets中已经处理了
        return []
    
    def clean_and_validate_data(self, data: List[Dict]) -> List[Dict]:
        """清洗和验证数据"""
        logger.info("开始数据清洗和验证...")
        
        cleaned_data = []
        seen_hashes = set()
        
        for item in data:
            # 1. 基本字段检查
            if not self._validate_basic_fields(item):
                continue
            
            # 2. 内容质量检查
            if not self._validate_content_quality(item):
                continue
            
            # 3. 去重检查
            content_hash = self._get_content_hash(item['content'])
            if content_hash in seen_hashes:
                self.dataset_stats['quality_issues'].append(f"重复内容: {content_hash[:8]}")
                continue
            seen_hashes.add(content_hash)
            
            # 4. 标准化处理
            cleaned_item = self._standardize_item(item)
            cleaned_data.append(cleaned_item)
        
        logger.info(f"数据清洗完成: {len(data)} -> {len(cleaned_data)}")
        return cleaned_data
    
    def _validate_basic_fields(self, item: Dict) -> bool:
        """验证基本字段"""
        required_fields = ['content', 'label']
        
        for field in required_fields:
            if field not in item:
                self.dataset_stats['quality_issues'].append(f"缺少字段: {field}")
                return False
        
        # 检查标签值
        if item['label'] not in [0, 1]:
            self.dataset_stats['quality_issues'].append(f"无效标签: {item['label']}")
            return False
        
        return True
    
    def _validate_content_quality(self, item: Dict) -> bool:
        """验证内容质量"""
        content = item['content']
        
        # 检查内容长度
        if len(content.strip()) < 10:
            self.dataset_stats['quality_issues'].append("内容过短")
            return False
        
        if len(content) > 10000:
            self.dataset_stats['quality_issues'].append("内容过长")
            return False
        
        # 检查是否包含基本邮件结构
        has_subject = 'subject:' in content.lower() or '主题:' in content
        has_from = 'from:' in content.lower() or '发件人:' in content
        
        if not (has_subject or has_from):
            # 如果没有邮件头，检查是否是纯文本内容
            if len(content.split()) < 5:
                self.dataset_stats['quality_issues'].append("内容结构不完整")
                return False
        
        return True
    
    def _get_content_hash(self, content: str) -> str:
        """获取内容哈希值"""
        # 标准化内容后计算哈希
        normalized = re.sub(r'\s+', ' ', content.lower().strip())
        return hashlib.md5(normalized.encode('utf-8')).hexdigest()
    
    def _standardize_item(self, item: Dict) -> Dict:
        """标准化数据项"""
        standardized = {
            'id': self._generate_id(item),
            'content': item['content'].strip(),
            'label': int(item['label']),
            'type': item.get('type', 'unknown'),
            'source': item.get('source', 'unknown'),
            'language': self._detect_language(item['content']),
            'length': len(item['content']),
            'word_count': len(item['content'].split()),
            'has_urls': bool(re.search(r'http[s]?://', item['content'])),
            'has_email': bool(re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', item['content']))
        }
        
        # 保留其他字段
        for key, value in item.items():
            if key not in standardized:
                standardized[key] = value
        
        return standardized
    
    def _generate_id(self, item: Dict) -> str:
        """生成唯一ID"""
        content_hash = self._get_content_hash(item['content'])
        source = item.get('source', 'unknown')
        return f"{source}_{content_hash[:12]}"
    
    def _detect_language(self, content: str) -> str:
        """检测语言"""
        # 简单的语言检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        total_chars = len(content)
        
        if chinese_chars > total_chars * 0.1:
            return 'zh'
        else:
            return 'en'
    
    def create_balanced_dataset(self, data: List[Dict], 
                              target_size: int = 10000) -> Dict[str, List[Dict]]:
        """创建平衡的数据集"""
        logger.info(f"创建平衡数据集，目标大小: {target_size}")
        
        # 按标签分组
        normal_emails = [item for item in data if item['label'] == 0]
        phishing_emails = [item for item in data if item['label'] == 1]
        
        logger.info(f"正常邮件: {len(normal_emails)}, 钓鱼邮件: {len(phishing_emails)}")
        
        # 计算每类的目标数量
        target_normal = int(target_size * 0.6)  # 60% 正常邮件
        target_phishing = int(target_size * 0.4)  # 40% 钓鱼邮件
        
        # 采样
        import random
        random.seed(42)  # 确保可重现
        
        if len(normal_emails) >= target_normal:
            selected_normal = random.sample(normal_emails, target_normal)
        else:
            selected_normal = normal_emails
            logger.warning(f"正常邮件不足，实际: {len(normal_emails)}, 目标: {target_normal}")
        
        if len(phishing_emails) >= target_phishing:
            selected_phishing = random.sample(phishing_emails, target_phishing)
        else:
            selected_phishing = phishing_emails
            logger.warning(f"钓鱼邮件不足，实际: {len(phishing_emails)}, 目标: {target_phishing}")
        
        # 创建数据集分割
        all_selected = selected_normal + selected_phishing
        random.shuffle(all_selected)
        
        # 分割比例：70% 训练，15% 验证，15% 测试
        total = len(all_selected)
        train_size = int(total * 0.7)
        val_size = int(total * 0.15)
        
        dataset_splits = {
            'train': all_selected[:train_size],
            'validation': all_selected[train_size:train_size + val_size],
            'test': all_selected[train_size + val_size:]
        }
        
        logger.info("数据集分割完成:")
        for split_name, split_data in dataset_splits.items():
            normal_count = sum(1 for item in split_data if item['label'] == 0)
            phishing_count = sum(1 for item in split_data if item['label'] == 1)
            logger.info(f"  {split_name}: {len(split_data)} 样本 (正常: {normal_count}, 钓鱼: {phishing_count})")
        
        return dataset_splits
    
    def save_dataset(self, dataset_splits: Dict[str, List[Dict]], format_type: str = 'all'):
        """保存数据集"""
        logger.info(f"保存数据集，格式: {format_type}")
        
        for split_name, split_data in dataset_splits.items():
            split_dir = self.output_dir / split_name
            split_dir.mkdir(exist_ok=True)
            
            if format_type in ['json', 'all']:
                # 保存JSON格式
                json_path = split_dir / f"{split_name}.json"
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(split_data, f, ensure_ascii=False, indent=2)
            
            if format_type in ['csv', 'all']:
                # 保存CSV格式
                csv_path = split_dir / f"{split_name}.csv"
                df = pd.DataFrame(split_data)
                df.to_csv(csv_path, index=False, encoding='utf-8')
            
            if format_type in ['txt', 'all']:
                # 保存纯文本格式（仅内容和标签）
                txt_path = split_dir / f"{split_name}.txt"
                with open(txt_path, 'w', encoding='utf-8') as f:
                    for item in split_data:
                        f.write(f"{item['label']}\t{item['content']}\n")
        
        logger.info(f"数据集保存完成: {self.output_dir}")
    
    def generate_dataset_report(self, dataset_splits: Dict[str, List[Dict]]) -> Dict:
        """生成数据集报告"""
        logger.info("生成数据集报告...")
        
        report = {
            'summary': {
                'total_samples': sum(len(split_data) for split_data in dataset_splits.values()),
                'splits': {}
            },
            'label_distribution': {},
            'language_distribution': {},
            'source_distribution': {},
            'content_statistics': {},
            'quality_issues': self.dataset_stats['quality_issues']
        }
        
        # 分割统计
        for split_name, split_data in dataset_splits.items():
            normal_count = sum(1 for item in split_data if item['label'] == 0)
            phishing_count = sum(1 for item in split_data if item['label'] == 1)
            
            report['summary']['splits'][split_name] = {
                'total': len(split_data),
                'normal': normal_count,
                'phishing': phishing_count,
                'balance_ratio': normal_count / phishing_count if phishing_count > 0 else 0
            }
        
        # 整体统计
        all_data = []
        for split_data in dataset_splits.values():
            all_data.extend(split_data)
        
        # 标签分布
        label_counts = Counter(item['label'] for item in all_data)
        report['label_distribution'] = dict(label_counts)
        
        # 语言分布
        language_counts = Counter(item.get('language', 'unknown') for item in all_data)
        report['language_distribution'] = dict(language_counts)
        
        # 来源分布
        source_counts = Counter(item.get('source', 'unknown') for item in all_data)
        report['source_distribution'] = dict(source_counts)
        
        # 内容统计
        lengths = [item.get('length', 0) for item in all_data]
        word_counts = [item.get('word_count', 0) for item in all_data]
        
        report['content_statistics'] = {
            'avg_length': sum(lengths) / len(lengths) if lengths else 0,
            'avg_word_count': sum(word_counts) / len(word_counts) if word_counts else 0,
            'min_length': min(lengths) if lengths else 0,
            'max_length': max(lengths) if lengths else 0,
            'emails_with_urls': sum(1 for item in all_data if item.get('has_urls', False)),
            'emails_with_email_addresses': sum(1 for item in all_data if item.get('has_email', False))
        }
        
        # 保存报告
        report_path = self.output_dir / 'dataset_report.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据集报告保存到: {report_path}")
        return report
    
    def create_final_dataset(self, target_size: int = 10000):
        """创建最终数据集"""
        logger.info("开始创建最终数据集...")
        
        # 1. 收集所有数据
        all_data = self.collect_all_data()
        
        # 2. 清洗和验证
        cleaned_data = self.clean_and_validate_data(all_data)
        
        # 3. 创建平衡数据集
        dataset_splits = self.create_balanced_dataset(cleaned_data, target_size)
        
        # 4. 保存数据集
        self.save_dataset(dataset_splits, format_type='all')
        
        # 5. 生成报告
        report = self.generate_dataset_report(dataset_splits)
        
        logger.info("最终数据集创建完成!")
        return dataset_splits, report


def main():
    """主函数"""
    print("🚀 数据集管理工具启动")
    print("=" * 50)
    
    manager = DatasetManager()
    
    # 创建最终数据集
    dataset_splits, report = manager.create_final_dataset(target_size=10000)
    
    # 显示报告摘要
    print("\n📊 数据集报告摘要:")
    print(f"总样本数: {report['summary']['total_samples']}")
    
    print("\n各分割统计:")
    for split_name, stats in report['summary']['splits'].items():
        print(f"  {split_name}: {stats['total']} 样本 "
              f"(正常: {stats['normal']}, 钓鱼: {stats['phishing']})")
    
    print(f"\n标签分布: {report['label_distribution']}")
    print(f"语言分布: {report['language_distribution']}")
    
    print(f"\n内容统计:")
    print(f"  平均长度: {report['content_statistics']['avg_length']:.1f} 字符")
    print(f"  平均词数: {report['content_statistics']['avg_word_count']:.1f} 词")
    print(f"  包含URL的邮件: {report['content_statistics']['emails_with_urls']}")
    
    if report['quality_issues']:
        print(f"\n⚠️ 质量问题: {len(report['quality_issues'])} 个")
    
    print(f"\n✅ 最终数据集创建完成!")
    print(f"数据保存在: {manager.output_dir}")


if __name__ == "__main__":
    main()
