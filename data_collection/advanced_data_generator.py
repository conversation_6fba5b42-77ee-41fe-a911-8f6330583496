"""
高级邮件数据生成器
利用对抗学习和数据增强技术生成高质量的测试数据

功能：
1. 基于对抗学习的邮件变种生成
2. 多语言邮件生成（中英文）
3. 未知类型钓鱼邮件生成（用于零样本学习）
4. 数据质量评估和过滤
"""

import sys
import json
import random
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

logger = logging.getLogger(__name__)


class AdvancedDataGenerator:
    """高级邮件数据生成器"""
    
    def __init__(self, output_dir: str = "data/generated"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化语义替换词典
        self.semantic_substitutions = {
            # 紧急词汇
            'urgent': ['immediate', 'critical', 'pressing', 'vital', 'emergency'],
            'immediately': ['right away', 'at once', 'without delay', 'instantly'],
            'asap': ['as soon as possible', 'urgently', 'right away'],
            
            # 行动词汇
            'click': ['tap', 'select', 'press', 'activate', 'access'],
            'verify': ['confirm', 'validate', 'authenticate', 'check', 'review'],
            'update': ['refresh', 'renew', 'modify', 'revise', 'change'],
            
            # 账户相关
            'account': ['profile', 'credentials', 'login', 'access', 'membership'],
            'suspended': ['blocked', 'frozen', 'disabled', 'restricted', 'locked'],
            'expire': ['end', 'terminate', 'lapse', 'conclude', 'finish'],
            
            # 安全相关
            'security': ['protection', 'safety', 'privacy', 'defense', 'safeguard'],
            'breach': ['violation', 'compromise', 'intrusion', 'attack'],
            'unauthorized': ['illegal', 'forbidden', 'prohibited', 'invalid']
        }
        
        # 中文语义替换词典
        self.chinese_substitutions = {
            '紧急': ['急迫', '重要', '关键', '严重'],
            '立即': ['马上', '即刻', '迅速', '尽快'],
            '验证': ['确认', '核实', '检查', '审核'],
            '账户': ['帐户', '账号', '用户', '会员'],
            '安全': ['保护', '防护', '安保', '保障'],
            '点击': ['单击', '按下', '选择', '访问']
        }
    
    def generate_adversarial_variants(self, base_emails: List[str], 
                                    variants_per_email: int = 3) -> List[Dict]:
        """
        生成对抗性邮件变种
        
        Args:
            base_emails: 基础邮件列表
            variants_per_email: 每个邮件生成的变种数量
            
        Returns:
            生成的对抗性邮件列表
        """
        logger.info(f"生成对抗性邮件变种，基础邮件数: {len(base_emails)}")
        
        adversarial_emails = []
        
        for base_email in base_emails:
            for variant_id in range(variants_per_email):
                # 应用不同的变换策略
                if variant_id == 0:
                    # 语义替换
                    variant = self._apply_semantic_substitution(base_email)
                    strategy = "semantic_substitution"
                elif variant_id == 1:
                    # 句法重构
                    variant = self._apply_syntactic_transformation(base_email)
                    strategy = "syntactic_transformation"
                else:
                    # 混合策略
                    variant = self._apply_semantic_substitution(base_email)
                    variant = self._apply_syntactic_transformation(variant)
                    strategy = "mixed_transformation"
                
                adversarial_emails.append({
                    'content': variant,
                    'label': 1,  # 钓鱼邮件
                    'type': 'adversarial_phishing',
                    'generation_strategy': strategy,
                    'base_email_hash': hash(base_email) % 10000
                })
        
        logger.info(f"生成了 {len(adversarial_emails)} 个对抗性邮件变种")
        return adversarial_emails
    
    def _apply_semantic_substitution(self, text: str) -> str:
        """应用语义替换"""
        result = text
        
        # 英文替换
        for original, substitutes in self.semantic_substitutions.items():
            if original.lower() in result.lower():
                substitute = random.choice(substitutes)
                # 保持原始大小写
                if original.isupper():
                    substitute = substitute.upper()
                elif original.istitle():
                    substitute = substitute.title()
                
                result = re.sub(re.escape(original), substitute, result, flags=re.IGNORECASE)
        
        # 中文替换
        for original, substitutes in self.chinese_substitutions.items():
            if original in result:
                substitute = random.choice(substitutes)
                result = result.replace(original, substitute)
        
        return result
    
    def _apply_syntactic_transformation(self, text: str) -> str:
        """应用句法变换"""
        # 分割成句子
        sentences = re.split(r'[.!?]+', text)
        transformed_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # 应用不同的句法变换
            transformation_type = random.choice(['passive_to_active', 'reorder', 'expand'])
            
            if transformation_type == 'passive_to_active':
                transformed = self._passive_to_active(sentence)
            elif transformation_type == 'reorder':
                transformed = self._reorder_sentence_elements(sentence)
            else:
                transformed = self._expand_sentence(sentence)
            
            transformed_sentences.append(transformed)
        
        return '. '.join(transformed_sentences) + '.'
    
    def _passive_to_active(self, sentence: str) -> str:
        """被动语态转主动语态"""
        # 简单的被动转主动规则
        patterns = [
            (r'(\w+) is (\w+ed) by (\w+)', r'\3 \2 \1'),
            (r'(\w+) was (\w+ed) by (\w+)', r'\3 \2 \1'),
            (r'(\w+) has been (\w+ed) by (\w+)', r'\3 has \2 \1'),
            (r'Your account is suspended', 'We suspended your account'),
            (r'Your account has been limited', 'We limited your account'),
            (r'Access is denied', 'We deny access')
        ]
        
        result = sentence
        for pattern, replacement in patterns:
            result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)
        
        return result
    
    def _reorder_sentence_elements(self, sentence: str) -> str:
        """重排句子元素"""
        # 简单的句子重排
        words = sentence.split()
        if len(words) > 5:
            # 随机交换一些词的位置（保持语法合理性）
            mid = len(words) // 2
            if random.random() < 0.3:  # 30%概率进行重排
                words[1:mid], words[mid:-1] = words[mid:-1], words[1:mid]
        
        return ' '.join(words)
    
    def _expand_sentence(self, sentence: str) -> str:
        """扩展句子"""
        # 添加一些修饰词和短语
        expansions = [
            'Please note that ',
            'It is important to understand that ',
            'For your security, ',
            'To protect your account, ',
            'Due to recent security updates, '
        ]
        
        if random.random() < 0.4:  # 40%概率添加前缀
            prefix = random.choice(expansions)
            sentence = prefix + sentence.lower()
        
        return sentence
    
    def generate_unknown_phishing_types(self, count: int = 1000) -> List[Dict]:
        """
        生成未知类型的钓鱼邮件（用于零样本学习验证）
        
        Args:
            count: 生成数量
            
        Returns:
            未知类型钓鱼邮件列表
        """
        logger.info(f"生成 {count} 个未知类型钓鱼邮件")
        
        # 新兴钓鱼攻击模板
        unknown_templates = [
            # AI助手钓鱼
            """Subject: Your AI Assistant Needs Verification
From: ai-support@{domain}
To: <EMAIL>
Date: {date}

Hello,

Your AI assistant subscription requires immediate verification to continue service. 
Our advanced AI detected unusual patterns in your usage.

Verify your AI access: http://{phishing_domain}/ai-verify

Features at risk:
- Smart recommendations
- Automated responses  
- Premium AI models

Verify within 6 hours to maintain access.

AI Support Team
""",
            
            # 加密货币钓鱼
            """Subject: Crypto Wallet Security Alert - Action Required
From: security@{crypto_domain}
To: <EMAIL>
Date: {date}

URGENT: Suspicious blockchain activity detected on your wallet.

Wallet Address: {wallet_address}
Suspicious Transactions: {tx_count}
Risk Level: HIGH

Secure your crypto assets immediately:
http://{phishing_domain}/crypto-secure

Failure to act may result in:
- Asset freezing
- Wallet suspension
- Loss of funds

Crypto Security Team
""",
            
            # 元宇宙/NFT钓鱼
            """Subject: Your NFT Collection - Ownership Verification Required
From: nft-verify@{nft_domain}
To: <EMAIL>
Date: {date}

Dear NFT Collector,

New regulations require verification of NFT ownership. Your valuable collection 
may be delisted without immediate action.

Collection Value: ${nft_value}
Items at Risk: {nft_count} NFTs
Deadline: {deadline}

Verify ownership: http://{phishing_domain}/nft-verify

Protect your digital assets now.

NFT Marketplace Team
""",
            
            # 远程工作钓鱼
            """Subject: Remote Work Security Update - VPN Reconfiguration
From: it-security@{company_domain}
To: <EMAIL>
Date: {date}

Important: Company VPN security update required for all remote workers.

Your current VPN certificate expires in 24 hours. Update immediately to 
maintain secure access to company resources.

Update VPN Settings: http://{phishing_domain}/vpn-update

Required Information:
- Employee ID
- Current VPN credentials
- Multi-factor authentication codes

IT Security Department
""",
            
            # 社交媒体钓鱼
            """Subject: Your Social Media Account - Copyright Violation Report
From: legal@{social_domain}
To: <EMAIL>
Date: {date}

LEGAL NOTICE: Copyright infringement detected on your account.

Reported Content: {content_type}
Violation Type: Unauthorized use
Status: Under review

Your account will be permanently banned unless you respond within 48 hours.

Appeal the violation: http://{phishing_domain}/copyright-appeal

Provide proof of ownership or licensing rights.

Legal Department
{social_platform}
"""
        ]
        
        unknown_emails = []
        
        for _ in range(count):
            template = random.choice(unknown_templates)
            
            # 生成随机变量
            domains = [f"secure-{random.randint(1000,9999)}.com", 
                      f"verify-{random.randint(1000,9999)}.net",
                      f"auth-{random.randint(1000,9999)}.org"]
            
            phishing_domains = [f"secure-verify-{random.randint(100,999)}.com",
                              f"account-check-{random.randint(100,999)}.net",
                              f"user-confirm-{random.randint(100,999)}.org"]
            
            # 生成日期
            days_ago = random.randint(0, 7)
            date = (datetime.now() - timedelta(days=days_ago)).strftime("%a, %d %b %Y %H:%M:%S +0000")
            
            # 填充模板
            email_content = template.format(
                domain=random.choice(domains),
                phishing_domain=random.choice(phishing_domains),
                date=date,
                crypto_domain=f"crypto-{random.randint(100,999)}.com",
                wallet_address=f"0x{random.randint(10**15, 10**16-1):016x}",
                tx_count=random.randint(3, 15),
                nft_domain=f"nft-{random.randint(100,999)}.com",
                nft_value=f"{random.randint(5000, 50000):,}",
                nft_count=random.randint(5, 50),
                deadline=f"{random.randint(12, 72)} hours",
                company_domain=f"company-{random.randint(100,999)}.com",
                social_domain=f"social-{random.randint(100,999)}.com",
                content_type=random.choice(["Image", "Video", "Audio", "Text"]),
                social_platform=random.choice(["SocialNet", "ConnectApp", "SharePlatform"])
            )
            
            unknown_emails.append({
                'content': email_content,
                'label': 1,  # 钓鱼邮件
                'type': 'unknown_phishing',
                'attack_category': random.choice(['ai_phishing', 'crypto_phishing', 'nft_phishing', 
                                                'remote_work_phishing', 'social_media_phishing'])
            })
        
        logger.info(f"生成了 {len(unknown_emails)} 个未知类型钓鱼邮件")
        return unknown_emails
    
    def generate_multilingual_emails(self, count: int = 500) -> List[Dict]:
        """生成多语言邮件（中英文）"""
        logger.info(f"生成 {count} 个多语言邮件")
        
        # 中文钓鱼邮件模板
        chinese_templates = [
            """主题: 紧急：您的银行账户安全警报
发件人: security@{bank_domain}
收件人: <EMAIL>
日期: {date}

尊敬的客户：

我们检测到您的账户存在可疑活动。如果您不在24小时内验证您的信息，您的账户将被暂停。

立即验证：http://{phishing_domain}/verify

请提供：
- 账户号码
- 密码
- 身份证号码

如不回应将导致账户永久关闭。

此致
安全团队
{bank_name}
""",
            
            """主题: 微信支付安全提醒 - 需要验证
发件人: security@{wechat_domain}
收件人: <EMAIL>
日期: {date}

您好，

您的微信支付账户检测到异常交易，为保护您的资金安全，请立即验证身份。

异常交易金额：¥{amount}
交易时间：{transaction_time}
商户：{merchant}

验证链接：http://{phishing_domain}/wechat-verify

请在2小时内完成验证，否则账户将被冻结。

微信支付安全中心
""",
            
            """主题: 淘宝账户异常登录提醒
发件人: security@{taobao_domain}
收件人: <EMAIL>
日期: {date}

亲爱的用户：

您的淘宝账户在{login_location}有异常登录记录。

登录时间：{login_time}
登录设备：{device_type}
IP地址：{ip_address}

如非本人操作，请立即保护账户：
http://{phishing_domain}/taobao-secure

保护您的购物安全。

淘宝安全中心
"""
        ]
        
        multilingual_emails = []
        
        # 生成中文邮件
        for _ in range(count // 2):
            template = random.choice(chinese_templates)
            
            # 生成随机变量
            days_ago = random.randint(0, 30)
            date = (datetime.now() - timedelta(days=days_ago)).strftime("%Y年%m月%d日 %H:%M")
            
            email_content = template.format(
                bank_domain=f"bank-{random.randint(100,999)}.com",
                phishing_domain=f"secure-{random.randint(1000,9999)}.cn",
                date=date,
                bank_name=random.choice(["工商银行", "建设银行", "农业银行"]),
                wechat_domain=f"wechat-{random.randint(100,999)}.com",
                amount=f"{random.randint(100, 5000)}",
                transaction_time=date,
                merchant=random.choice(["在线商城", "数码商店", "服装店"]),
                taobao_domain=f"taobao-{random.randint(100,999)}.com",
                login_location=random.choice(["北京", "上海", "广州", "深圳"]),
                login_time=date,
                device_type=random.choice(["iPhone", "Android", "Windows PC"]),
                ip_address=f"{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}"
            )
            
            multilingual_emails.append({
                'content': email_content,
                'label': 1,
                'type': 'chinese_phishing',
                'language': 'zh'
            })
        
        # 生成英文邮件（使用之前的模板）
        for _ in range(count - count // 2):
            # 这里可以复用之前的英文模板
            multilingual_emails.append({
                'content': "English phishing email template...",  # 简化示例
                'label': 1,
                'type': 'english_phishing',
                'language': 'en'
            })
        
        logger.info(f"生成了 {len(multilingual_emails)} 个多语言邮件")
        return multilingual_emails
    
    def save_generated_data(self, data: List[Dict], filename: str):
        """保存生成的数据"""
        output_path = self.output_dir / f"{filename}.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"保存 {len(data)} 条数据到 {output_path}")
    
    def generate_complete_dataset(self):
        """生成完整的高级数据集"""
        logger.info("开始生成完整的高级数据集")
        
        # 1. 生成基础钓鱼邮件（用于生成对抗样本）
        base_phishing_emails = [
            "Urgent! Your account will be suspended. Click here to verify.",
            "Security alert: Unauthorized access detected. Verify now.",
            "Your payment failed. Update your information immediately."
        ]
        
        # 2. 生成对抗性变种
        adversarial_data = self.generate_adversarial_variants(base_phishing_emails, 5)
        self.save_generated_data(adversarial_data, "adversarial_phishing")
        
        # 3. 生成未知类型钓鱼邮件
        unknown_data = self.generate_unknown_phishing_types(1000)
        self.save_generated_data(unknown_data, "unknown_phishing")
        
        # 4. 生成多语言邮件
        multilingual_data = self.generate_multilingual_emails(500)
        self.save_generated_data(multilingual_data, "multilingual_phishing")
        
        # 5. 合并所有数据
        all_generated_data = adversarial_data + unknown_data + multilingual_data
        self.save_generated_data(all_generated_data, "complete_generated_dataset")
        
        logger.info(f"完整数据集生成完成，总计 {len(all_generated_data)} 条数据")
        
        return {
            'adversarial': len(adversarial_data),
            'unknown_types': len(unknown_data),
            'multilingual': len(multilingual_data),
            'total': len(all_generated_data)
        }


def main():
    """主函数"""
    print("🚀 高级邮件数据生成器启动")
    print("=" * 50)
    
    generator = AdvancedDataGenerator()
    
    # 生成完整数据集
    stats = generator.generate_complete_dataset()
    
    print("\n📊 生成统计:")
    print(f"  对抗性邮件: {stats['adversarial']}")
    print(f"  未知类型钓鱼邮件: {stats['unknown_types']}")
    print(f"  多语言邮件: {stats['multilingual']}")
    print(f"  总计: {stats['total']}")
    
    print(f"\n✅ 高级数据生成完成!")
    print(f"数据保存在: {generator.output_dir}")


if __name__ == "__main__":
    main()
